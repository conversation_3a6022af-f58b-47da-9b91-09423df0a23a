"""
API测试模块
整合所有API相关测试，包括邮箱生成、邮件检索、自定义前缀等功能
"""
import pytest
import json
from datetime import datetime, timedelta, timezone
from unittest import mock


@pytest.mark.api
class TestEmailGeneration:
    """测试邮箱地址生成API"""

    def test_generate_address_success(self, client, app_instance, db_conn):
        """测试成功生成新的临时邮箱地址"""
        response = client.post('/api/generate-address',
                             json={},
                             content_type='application/json')

        assert response.status_code == 201
        result = response.get_json()
        assert result["success"] is True
        assert "data" in result

        data = result["data"]
        assert "address" in data
        assert "expires_at" in data
        assert app_instance.config['DOMAIN_NAME'] in data["address"]

        # 验证过期时间
        expires_dt_str = data["expires_at"]
        if expires_dt_str.endswith('Z'):
            expires_dt = datetime.fromisoformat(expires_dt_str)
        else:
            expires_dt = datetime.fromisoformat(expires_dt_str.replace('Z', '+00:00'))

        expiration_hours = app_instance.config.get('EMAIL_EXPIRATION_HOURS', 1)
        now_utc = datetime.now(timezone.utc)
        expected_expires_approx = now_utc + timedelta(hours=expiration_hours)
        assert abs((expires_dt - expected_expires_approx).total_seconds()) < 10

        # 验证数据库记录
        cursor = db_conn.cursor()
        cursor.execute("SELECT address, expires_at FROM temporary_emails WHERE address = ?", 
                      (data["address"],))
        record = cursor.fetchone()
        assert record is not None
        assert record["address"] == data["address"]

    def test_generate_address_with_custom_prefix(self, client, app_instance):
        """测试使用自定义前缀生成邮箱"""
        test_cases = [
            {"prefix": "mytest", "should_succeed": True},
            {"prefix": "test-123", "should_succeed": True},
            {"prefix": "123456", "should_succeed": True},
            {"prefix": "test@invalid", "should_succeed": False},
            {"prefix": "a" * 25, "should_succeed": False},  # 超长前缀
            {"prefix": "", "should_succeed": False},  # 空前缀
        ]

        for case in test_cases:
            response = client.post('/api/generate-address',
                                 json={"custom_prefix": case["prefix"]},
                                 content_type='application/json')

            if case["should_succeed"]:
                assert response.status_code == 201
                result = response.get_json()
                assert result["success"] is True
                address = result["data"]["address"]
                if case["prefix"]:
                    assert address.startswith(case["prefix"])
            else:
                assert response.status_code == 400
                result = response.get_json()
                assert result["success"] is False
                assert "error" in result

    def test_generate_address_with_session_id(self, client, app_instance):
        """测试使用session_id生成邮箱"""
        session_id = "test_session_123"
        
        # 生成第一个邮箱
        response1 = client.post('/api/generate-address',
                               json={"session_id": session_id, "custom_prefix": "first"},
                               content_type='application/json')
        assert response1.status_code == 201
        result1 = response1.get_json()
        email1 = result1["data"]["address"]

        # 生成第二个邮箱
        response2 = client.post('/api/generate-address',
                               json={"session_id": session_id, "custom_prefix": "second"},
                               content_type='application/json')
        assert response2.status_code == 201
        result2 = response2.get_json()
        email2 = result2["data"]["address"]

        # 验证两个邮箱不同
        assert email1 != email2

        # 验证历史记录
        history_response = client.get(f'/api/email-history?session_id={session_id}')
        assert history_response.status_code == 200
        history_result = history_response.get_json()
        assert history_result["success"] is True
        
        history = history_result["data"]["history"]
        assert len(history) == 2
        
        # 验证最新邮箱为活跃状态
        assert history[0]["is_active"] is True
        assert history[1]["is_active"] is False

    def test_generate_address_domain_not_configured(self, client, app_instance):
        """测试域名未配置时的处理"""
        original_domain = app_instance.config['DOMAIN_NAME']
        app_instance.config['DOMAIN_NAME'] = None

        response = client.post('/api/generate-address',
                             json={},
                             content_type='application/json')
        
        assert response.status_code == 503
        result = response.get_json()
        assert "域名未设置" in result["error"]

        # 恢复原配置
        app_instance.config['DOMAIN_NAME'] = original_domain

    def test_generate_address_retry_mechanism(self, client, app_instance, db_conn):
        """测试地址生成的重试机制"""
        domain_name = app_instance.config['DOMAIN_NAME']
        existing_random = "aaaaaa"
        existing_email = f"{existing_random}@{domain_name}"

        # 预先插入一个邮箱地址
        now_utc = datetime.now(timezone.utc)
        expires_at_dt = now_utc + timedelta(hours=1)
        expires_at_iso = expires_at_dt.isoformat()

        cursor = db_conn.cursor()
        cursor.execute(
            "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
            (existing_email, expires_at_iso, now_utc.isoformat())
        )
        db_conn.commit()

        # 模拟随机生成器，第一次生成冲突的地址，第二次生成不冲突的地址
        with mock.patch('secrets.token_hex', side_effect=["aaaaaa", "bbbbbb"]):
            response = client.post('/api/generate-address',
                                 json={},
                                 content_type='application/json')

        assert response.status_code == 201
        result = response.get_json()
        assert result["success"] is True
        data = result["data"]
        assert data["address"] == f"bbbbbb@{domain_name}"


@pytest.mark.api
class TestEmailRetrieval:
    """测试邮件检索API"""

    def test_get_emails_for_address(self, client, app_instance, db_conn):
        """测试获取指定地址的邮件"""
        # 准备测试数据
        test_address = "test123@" + app_instance.config['DOMAIN_NAME']
        now_utc = datetime.now(timezone.utc)
        expires_at = (now_utc + timedelta(days=1)).isoformat()

        # 插入测试邮箱
        cursor = db_conn.cursor()
        cursor.execute("DELETE FROM temporary_emails WHERE address = ?", (test_address,))
        cursor.execute(
            "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
            (test_address, expires_at, now_utc.isoformat())
        )
        email_id = cursor.lastrowid

        # 插入测试邮件
        cursor.execute(
            """INSERT INTO received_mails
               (email_address_id, sender, subject, body_text, body_html, received_at)
               VALUES (?, ?, ?, ?, ?, ?)""",
            (email_id, "<EMAIL>", "Test Subject", "Test Body", 
             "<p>Test HTML</p>", now_utc.isoformat())
        )
        db_conn.commit()

        # 测试API
        response = client.get(f'/api/emails?address={test_address}')
        assert response.status_code == 200

        result = response.get_json()
        assert result["success"] is True
        assert "data" in result

        data = result["data"]
        assert "emails" in data
        emails = data["emails"]
        assert len(emails) == 1

        email = emails[0]
        assert email["sender"] == "<EMAIL>"
        assert email["subject"] == "Test Subject"
        assert "Test Body" in email["summary"]

    def test_get_emails_invalid_address(self, client):
        """测试获取无效地址的邮件"""
        response = client.get('/api/emails?address=<EMAIL>')
        assert response.status_code == 404

    def test_get_emails_expired_address(self, client, app_instance, db_conn):
        """测试获取已过期地址的邮件"""
        test_address = "expired@" + app_instance.config['DOMAIN_NAME']
        now_utc = datetime.now(timezone.utc)
        expires_at = (now_utc - timedelta(hours=2)).isoformat()  # 已过期

        cursor = db_conn.cursor()
        cursor.execute(
            "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
            (test_address, expires_at, now_utc.isoformat())
        )
        db_conn.commit()

        response = client.get(f'/api/emails?address={test_address}')
        assert response.status_code == 410  # Gone - 资源不再可用


@pytest.mark.api
class TestEmailHistory:
    """测试邮箱历史记录API"""

    def test_get_email_history_empty(self, client):
        """测试获取空历史记录"""
        session_id = "empty_session"
        response = client.get(f'/api/email-history?session_id={session_id}')
        
        assert response.status_code == 200
        result = response.get_json()
        assert result["success"] is True
        assert result["data"]["history"] == []

    def test_get_email_history_missing_session_id(self, client):
        """测试缺少session_id参数"""
        response = client.get('/api/email-history')
        assert response.status_code == 400
        result = response.get_json()
        assert "session_id" in result["error"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
