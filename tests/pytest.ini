[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short --strict-markers
markers =
    unit: 单元测试 - 测试单个函数或方法
    integration: 集成测试 - 测试组件间的交互
    functional: 功能测试 - 测试完整的用户场景
    api: API测试 - 测试API端点
    frontend: 前端测试 - 测试前端相关功能
    performance: 性能测试 - 测试性能指标
    slow: 慢速测试 - 运行时间较长的测试
    mail_handler: 邮件处理器测试
    cleanup: 清理脚本测试

# 测试过滤器示例:
# pytest -m "unit"                    # 只运行单元测试
# pytest -m "not slow"                # 排除慢速测试
# pytest -m "api and not slow"        # 运行API测试但排除慢速测试
# pytest -m "unit or integration"     # 运行单元测试和集成测试
