"""
性能测试模块
测试应用的性能指标，包括响应时间、并发处理、内存使用等
"""
import pytest
import time
import concurrent.futures
import threading
import os
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
from datetime import datetime, timezone, timedelta


@pytest.mark.performance
@pytest.mark.slow
class TestResponseTimePerformance:
    """响应时间性能测试"""

    def test_api_response_time_generate_address(self, client, clear_rate_limit):
        """测试生成地址API的响应时间"""
        clear_rate_limit()

        response_times = []
        num_requests = 10

        for _ in range(num_requests):
            start_time = time.time()
            response = client.post('/api/generate-address',
                                 json={},
                                 content_type='application/json')
            end_time = time.time()

            assert response.status_code in [201, 429]  # 成功或被限流
            response_times.append(end_time - start_time)

        # 计算性能指标
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)

        # 性能断言
        assert avg_response_time < 0.5, f"Average response time {avg_response_time:.3f}s is too slow"
        assert max_response_time < 1.0, f"Max response time {max_response_time:.3f}s is too slow"

    def test_api_response_time_get_emails(self, client, app_instance, db_conn):
        """测试获取邮件API的响应时间"""
        # 准备测试数据
        response = client.post('/api/generate-address',
                              json={"custom_prefix": "perftest"},
                              content_type='application/json')
        assert response.status_code == 201
        result = response.get_json()
        test_address = result["data"]["address"]

        # 插入一些测试邮件
        cursor = db_conn.cursor()
        cursor.execute("SELECT id FROM temporary_emails WHERE address = ?", (test_address,))
        email_id = cursor.fetchone()["id"]

        now_utc = datetime.now(timezone.utc)
        for i in range(50):  # 插入50封邮件
            cursor.execute(
                """INSERT INTO received_mails
                   (email_address_id, sender, subject, body_text, received_at)
                   VALUES (?, ?, ?, ?, ?)""",
                (email_id, f"sender{i}@test.com", f"Subject {i}",
                 f"Body content {i}", now_utc.isoformat())
            )
        db_conn.commit()

        # 测试响应时间
        response_times = []
        num_requests = 5

        for _ in range(num_requests):
            start_time = time.time()
            response = client.get(f'/api/emails?address={test_address}')
            end_time = time.time()

            assert response.status_code == 200
            response_times.append(end_time - start_time)

        avg_response_time = sum(response_times) / len(response_times)
        assert avg_response_time < 1.0, f"Average response time {avg_response_time:.3f}s is too slow for 50 emails"

    def test_database_query_performance(self, app_instance, db_conn):
        """测试数据库查询性能"""
        # 插入大量测试数据
        cursor = db_conn.cursor()
        now_utc = datetime.now(timezone.utc)
        expires_at = (now_utc + timedelta(hours=1)).isoformat()

        # 批量插入邮箱地址
        addresses = []
        for i in range(1000):
            address = f"perftest{i}@example.com"
            addresses.append((address, expires_at, now_utc.isoformat()))

        cursor.executemany(
            "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
            addresses
        )
        db_conn.commit()

        # 测试查询性能
        start_time = time.time()
        cursor.execute("SELECT COUNT(*) FROM temporary_emails WHERE address LIKE 'perftest%'")
        result = cursor.fetchone()
        end_time = time.time()

        query_time = end_time - start_time
        assert query_time < 0.1, f"Database query time {query_time:.3f}s is too slow"
        # 允许一定的误差，因为可能有其他测试插入的数据
        assert result[0] >= 1000, f"Expected at least 1000 records, got {result[0]}"

        # 清理测试数据
        cursor.execute("DELETE FROM temporary_emails WHERE address LIKE 'perftest%'")
        db_conn.commit()


@pytest.mark.performance
@pytest.mark.slow
class TestConcurrencyPerformance:
    """并发性能测试"""

    def test_concurrent_address_generation_performance(self, client, app_instance, clear_rate_limit):
        """测试并发生成地址的性能"""
        clear_rate_limit()

        # 临时禁用测试模式以启用速率限制
        original_testing = app_instance.config['TESTING']
        app_instance.config['TESTING'] = False

        try:
            num_threads = 5
            requests_per_thread = 2

            def make_requests():
                thread_times = []
                for _ in range(requests_per_thread):
                    start_time = time.time()
                    response = client.post('/api/generate-address',
                                         json={},
                                         content_type='application/json')
                    end_time = time.time()
                    thread_times.append(end_time - start_time)
                return thread_times

            start_time = time.time()
            with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [executor.submit(make_requests) for _ in range(num_threads)]
                results = [f.result() for f in concurrent.futures.as_completed(futures)]
            end_time = time.time()

            total_time = end_time - start_time
            all_response_times = [time for thread_times in results for time in thread_times]

            # 性能断言
            avg_response_time = sum(all_response_times) / len(all_response_times)
            assert avg_response_time < 1.0, f"Average concurrent response time {avg_response_time:.3f}s is too slow"
            assert total_time < 5.0, f"Total concurrent execution time {total_time:.3f}s is too slow"

        finally:
            app_instance.config['TESTING'] = original_testing

    def test_database_concurrent_access_performance(self, app_instance, db_conn):
        """测试数据库并发访问性能"""
        db_path = app_instance.config['DATABASE_PATH']
        num_threads = 10
        operations_per_thread = 10

        def database_operations():
            import sqlite3
            thread_times = []

            with sqlite3.connect(db_path) as thread_conn:
                thread_conn.row_factory = sqlite3.Row
                cursor = thread_conn.cursor()

                for i in range(operations_per_thread):
                    start_time = time.time()

                    # 执行一个简单的数据库操作
                    thread_id = threading.get_ident()
                    test_address = f"thread{thread_id}_{i}@test.com"
                    now_utc = datetime.now(timezone.utc)
                    expires_at = (now_utc + timedelta(hours=1)).isoformat()

                    try:
                        cursor.execute(
                            "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
                            (test_address, expires_at, now_utc.isoformat())
                        )
                        thread_conn.commit()

                        cursor.execute("SELECT id FROM temporary_emails WHERE address = ?", (test_address,))
                        result = cursor.fetchone()

                        cursor.execute("DELETE FROM temporary_emails WHERE address = ?", (test_address,))
                        thread_conn.commit()

                        end_time = time.time()
                        thread_times.append(end_time - start_time)

                    except sqlite3.Error:
                        end_time = time.time()
                        thread_times.append(end_time - start_time)

            return thread_times

        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(database_operations) for _ in range(num_threads)]
            results = [f.result() for f in concurrent.futures.as_completed(futures)]
        end_time = time.time()

        total_time = end_time - start_time
        all_operation_times = [time for thread_times in results for time in thread_times]

        avg_operation_time = sum(all_operation_times) / len(all_operation_times)
        assert avg_operation_time < 0.1, f"Average database operation time {avg_operation_time:.3f}s is too slow"
        assert total_time < 10.0, f"Total concurrent database time {total_time:.3f}s is too slow"


@pytest.mark.performance
class TestMemoryPerformance:
    """内存性能测试"""

    @pytest.mark.skipif(not HAS_PSUTIL, reason="psutil not available")
    def test_memory_usage_during_operations(self, client, app_instance):
        """测试操作期间的内存使用"""
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 执行一系列操作
        for i in range(100):
            response = client.post('/api/generate-address',
                                 json={"custom_prefix": f"mem{i}"},
                                 content_type='application/json')
            if response.status_code == 429:  # 如果被限流，跳过
                break

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # 内存增长不应该过大
        assert memory_increase < 50, f"Memory increase {memory_increase:.2f}MB is too high"

    @pytest.mark.skipif(not HAS_PSUTIL, reason="psutil not available")
    def test_memory_leak_detection(self, client, app_instance):
        """测试内存泄漏检测"""
        process = psutil.Process(os.getpid())

        # 记录多个时间点的内存使用
        memory_samples = []

        for round_num in range(5):
            # 执行一轮操作
            for i in range(20):
                response = client.post('/api/generate-address',
                                     json={},
                                     content_type='application/json')
                if response.status_code == 429:
                    break

            # 记录内存使用
            memory_mb = process.memory_info().rss / 1024 / 1024
            memory_samples.append(memory_mb)

            # 短暂等待
            time.sleep(0.1)

        # 检查内存是否持续增长
        if len(memory_samples) >= 3:
            # 计算内存增长趋势
            memory_growth = memory_samples[-1] - memory_samples[0]

            # 允许一定的内存增长，但不应该过大
            assert memory_growth < 20, f"Potential memory leak detected: {memory_growth:.2f}MB growth"


@pytest.mark.performance
class TestScalabilityPerformance:
    """可扩展性性能测试"""

    def test_large_email_list_performance(self, client, app_instance, db_conn):
        """测试大量邮件列表的性能"""
        # 生成测试邮箱
        response = client.post('/api/generate-address',
                              json={"custom_prefix": "scalability"},
                              content_type='application/json')
        assert response.status_code == 201
        result = response.get_json()
        test_address = result["data"]["address"]

        # 获取邮箱ID
        cursor = db_conn.cursor()
        cursor.execute("SELECT id FROM temporary_emails WHERE address = ?", (test_address,))
        email_id = cursor.fetchone()["id"]

        # 插入大量邮件
        now_utc = datetime.now(timezone.utc)
        emails_data = []
        for i in range(500):  # 500封邮件
            emails_data.append((
                email_id,
                f"sender{i}@test.com",
                f"Subject {i}",
                f"Body content for email {i} " * 10,  # 较长的内容
                now_utc.isoformat()
            ))

        # 批量插入
        start_time = time.time()
        cursor.executemany(
            """INSERT INTO received_mails
               (email_address_id, sender, subject, body_text, received_at)
               VALUES (?, ?, ?, ?, ?)""",
            emails_data
        )
        db_conn.commit()
        insert_time = time.time() - start_time

        # 测试查询性能
        start_time = time.time()
        response = client.get(f'/api/emails?address={test_address}')
        query_time = time.time() - start_time

        assert response.status_code == 200
        result = response.get_json()
        emails = result["data"]["emails"]

        # 性能断言
        assert insert_time < 5.0, f"Bulk insert time {insert_time:.3f}s is too slow"
        assert query_time < 2.0, f"Large email list query time {query_time:.3f}s is too slow"
        assert len(emails) == 500


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "performance"])
