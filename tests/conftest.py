# tests/conftest.py
"""
统一的测试配置文件
包含所有测试所需的fixtures和配置
"""
import os
import tempfile
import pytest
import sqlite3
import logging
from datetime import datetime, timedelta, timezone
import sys
from pathlib import Path

# 确保能从项目根目录导入 app 模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from app import app as flask_app, init_db_schema, get_db_connection

# 注册自定义测试标记
def pytest_configure(config):
    """注册自定义测试标记"""
    config.addinivalue_line("markers", "unit: 单元测试")
    config.addinivalue_line("markers", "integration: 集成测试")
    config.addinivalue_line("markers", "functional: 功能测试")
    config.addinivalue_line("markers", "api: API测试")
    config.addinivalue_line("markers", "frontend: 前端测试")
    config.addinivalue_line("markers", "mail_handler: 邮件处理器测试")
    config.addinivalue_line("markers", "cleanup: 清理脚本测试")
    config.addinivalue_line("markers", "performance: 性能测试")
    config.addinivalue_line("markers", "slow: 慢速测试")


@pytest.fixture(scope='module')
def app_instance():
    """
    创建并配置一个新的 Flask app 实例用于每个测试模块。
    使用临时的 SQLite 文件作为数据库。
    """
    # 创建一个临时的数据库文件
    db_fd, db_path = tempfile.mkstemp(suffix='.db', prefix='test_tempmail_')
    db_path = Path(db_path)  # Ensure pathlib.Path object for compatibility

    # 配置 Flask app 进行测试
    flask_app.config.update({
        "TESTING": True,
        "DATABASE_PATH": db_path,
        "DOMAIN_NAME": "test.local", # 测试时使用的域名
        "EMAIL_EXPIRATION_HOURS": 1, # 测试时使用的过期小时数
        # 如果有其他需要为测试覆盖的配置，也在这里添加
    })

    # 在应用上下文中初始化测试数据库的表结构
    with flask_app.app_context():
        init_db_schema()

    yield flask_app # 提供 app 实例给测试函数

    # 测试完成后清理：关闭文件描述符并删除临时数据库文件
    os.close(db_fd)
    os.unlink(str(db_path))


@pytest.fixture(autouse=True)
def force_stream_logger(app_instance):
    import sys
    import logging
    for handler in list(app_instance.logger.handlers):
        app_instance.logger.removeHandler(handler)
    stream_handler = logging.StreamHandler(sys.stderr)
    stream_handler.setLevel(logging.ERROR)
    formatter = logging.Formatter('%(message)s')
    stream_handler.setFormatter(formatter)
    app_instance.logger.addHandler(stream_handler)
    app_instance.logger.setLevel(logging.ERROR)
    app_instance.logger.propagate = True  # 关键：让日志冒泡到 pytest caplog
    yield
    for handler in list(app_instance.logger.handlers):
        app_instance.logger.removeHandler(handler)


@pytest.fixture(scope='module')
def client(app_instance):
    """提供一个 Flask 测试客户端"""
    return app_instance.test_client()


@pytest.fixture(scope='function')
def db_conn(app_instance):
    """提供一个数据库连接，并在测试后关闭"""
    with app_instance.app_context():
        connection = get_db_connection()
        yield connection
        connection.close()


@pytest.fixture
def temp_db_path():
    """创建临时数据库文件路径"""
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
        db_path = f.name
    yield db_path
    # 清理临时文件
    if os.path.exists(db_path):
        os.unlink(db_path)


@pytest.fixture
def clear_rate_limit():
    """清理速率限制存储的fixture"""
    def _clear():
        try:
            from app import rate_limit
            if hasattr(rate_limit, "clear"):
                rate_limit.clear()
        except (ImportError, AttributeError):
            pass

    _clear()  # 测试前清理
    yield _clear
    _clear()  # 测试后清理