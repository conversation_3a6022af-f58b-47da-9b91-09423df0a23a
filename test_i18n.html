<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国际化测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .language-switcher {
            margin-bottom: 20px;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
        }
        .test-key {
            font-weight: bold;
            color: #333;
        }
        .test-value {
            color: #666;
            margin-left: 10px;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <h1>临时邮箱国际化测试</h1>
    
    <div class="language-switcher">
        <h2>语言切换</h2>
        <button onclick="switchLanguage('zh-CN')">中文</button>
        <button onclick="switchLanguage('en-US')">English</button>
        <p>当前语言: <span id="current-language"></span></p>
    </div>

    <div class="test-section">
        <h2>页面标题和描述</h2>
        <div class="test-item">
            <span class="test-key">页面标题:</span>
            <span class="test-value" id="test-page-title"></span>
        </div>
        <div class="test-item">
            <span class="test-key">页面描述:</span>
            <span class="test-value" id="test-page-description"></span>
        </div>
    </div>

    <div class="test-section">
        <h2>导航栏</h2>
        <div class="test-item">
            <span class="test-key">网站标题:</span>
            <span class="test-value" id="test-nav-title"></span>
        </div>
        <div class="test-item">
            <span class="test-key">首页:</span>
            <span class="test-value" id="test-nav-home"></span>
        </div>
        <div class="test-item">
            <span class="test-key">常见问题:</span>
            <span class="test-value" id="test-nav-faq"></span>
        </div>
        <div class="test-item">
            <span class="test-key">联系我们:</span>
            <span class="test-value" id="test-nav-contact"></span>
        </div>
    </div>

    <div class="test-section">
        <h2>按钮文本</h2>
        <div class="test-item">
            <span class="test-key">复制:</span>
            <span class="test-value" id="test-button-copy"></span>
        </div>
        <div class="test-item">
            <span class="test-key">刷新:</span>
            <span class="test-value" id="test-button-refresh"></span>
        </div>
        <div class="test-item">
            <span class="test-key">新邮箱:</span>
            <span class="test-value" id="test-button-new-email"></span>
        </div>
        <div class="test-item">
            <span class="test-key">自定义:</span>
            <span class="test-value" id="test-button-custom"></span>
        </div>
        <div class="test-item">
            <span class="test-key">历史记录:</span>
            <span class="test-value" id="test-button-history"></span>
        </div>
        <div class="test-item">
            <span class="test-key">删除并重置:</span>
            <span class="test-value" id="test-button-delete-reset"></span>
        </div>
    </div>

    <div class="test-section">
        <h2>状态文本</h2>
        <div class="test-item">
            <span class="test-key">已复制:</span>
            <span class="test-value" id="test-status-copied"></span>
        </div>
        <div class="test-item">
            <span class="test-key">刷新中:</span>
            <span class="test-value" id="test-status-refreshing"></span>
        </div>
        <div class="test-item">
            <span class="test-key">生成中:</span>
            <span class="test-value" id="test-status-generating"></span>
        </div>
        <div class="test-item">
            <span class="test-key">已生成:</span>
            <span class="test-value" id="test-status-generated"></span>
        </div>
        <div class="test-item">
            <span class="test-key">失败:</span>
            <span class="test-value" id="test-status-failed"></span>
        </div>
    </div>

    <div class="test-section">
        <h2>错误消息</h2>
        <div class="test-item">
            <span class="test-key">没有邮箱地址可复制:</span>
            <span class="test-value" id="test-error-no-email-copy"></span>
        </div>
        <div class="test-item">
            <span class="test-key">复制失败:</span>
            <span class="test-value" id="test-error-copy-failed"></span>
        </div>
        <div class="test-item">
            <span class="test-key">前缀不能为空:</span>
            <span class="test-value" id="test-error-prefix-empty"></span>
        </div>
        <div class="test-item">
            <span class="test-key">前缀太长:</span>
            <span class="test-value" id="test-error-prefix-too-long"></span>
        </div>
    </div>

    <!-- 引入国际化模块 -->
    <script src="static/js/i18n.js"></script>
    
    <script>
        // 初始化国际化
        const i18n = new I18n();
        
        function switchLanguage(lang) {
            i18n.setLanguage(lang);
            updateTestTexts();
            document.getElementById('current-language').textContent = lang;
        }
        
        function updateTestTexts() {
            // 页面标题和描述
            document.getElementById('test-page-title').textContent = i18n.t('page.title');
            document.getElementById('test-page-description').textContent = i18n.t('page.description');
            
            // 导航栏
            document.getElementById('test-nav-title').textContent = i18n.t('nav.title');
            document.getElementById('test-nav-home').textContent = i18n.t('nav.home');
            document.getElementById('test-nav-faq').textContent = i18n.t('nav.faq');
            document.getElementById('test-nav-contact').textContent = i18n.t('nav.contact');
            
            // 按钮文本
            document.getElementById('test-button-copy').textContent = i18n.t('button.copy');
            document.getElementById('test-button-refresh').textContent = i18n.t('button.refresh');
            document.getElementById('test-button-new-email').textContent = i18n.t('button.new_email');
            document.getElementById('test-button-custom').textContent = i18n.t('button.custom');
            document.getElementById('test-button-history').textContent = i18n.t('button.history');
            document.getElementById('test-button-delete-reset').textContent = i18n.t('button.delete_reset');
            
            // 状态文本
            document.getElementById('test-status-copied').textContent = i18n.t('status.copied');
            document.getElementById('test-status-refreshing').textContent = i18n.t('status.refreshing');
            document.getElementById('test-status-generating').textContent = i18n.t('status.generating');
            document.getElementById('test-status-generated').textContent = i18n.t('status.generated');
            document.getElementById('test-status-failed').textContent = i18n.t('status.failed');
            
            // 错误消息
            document.getElementById('test-error-no-email-copy').textContent = i18n.t('error.no_email_to_copy');
            document.getElementById('test-error-copy-failed').textContent = i18n.t('error.copy_failed');
            document.getElementById('test-error-prefix-empty').textContent = i18n.t('error.prefix_empty');
            document.getElementById('test-error-prefix-too-long').textContent = i18n.t('error.prefix_too_long');
        }
        
        // 初始化显示
        document.getElementById('current-language').textContent = i18n.getCurrentLanguage();
        updateTestTexts();
    </script>
</body>
</html>
