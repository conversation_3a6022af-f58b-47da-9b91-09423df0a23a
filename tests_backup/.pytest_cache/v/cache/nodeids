["test_api_config_validation.py::test_api_config_js", "test_api_config_validation.py::test_documentation", "test_api_config_validation.py::test_env_example", "test_api_config_validation.py::test_flask_config", "test_api_config_validation.py::test_main_js_integration", "test_api_config_validation.py::test_static_files", "test_api_config_validation.py::test_template_integration", "test_api_consolidated.py::TestEmailGeneration::test_generate_address_all_retries_failed", "test_api_consolidated.py::TestEmailGeneration::test_generate_address_api_rate_limited", "test_api_consolidated.py::TestEmailGeneration::test_generate_address_retry_mechanism", "test_api_consolidated.py::TestEmailGeneration::test_generate_address_success", "test_api_consolidated.py::TestEmailRetrieval::test_get_emails_expired_address", "test_api_consolidated.py::TestEmailRetrieval::test_get_emails_for_address", "test_api_consolidated.py::TestEmailRetrieval::test_get_emails_invalid_address", "test_app_consolidated.py::TestAPIEndpoints::test_delete_email_invalid_address", "test_app_consolidated.py::TestAPIEndpoints::test_delete_email_missing_address", "test_app_consolidated.py::TestAPIEndpoints::test_delete_email_not_found", "test_app_consolidated.py::TestAPIEndpoints::test_delete_email_success", "test_app_consolidated.py::TestAPIEndpoints::test_get_email_content_missing_address", "test_app_consolidated.py::TestAPIEndpoints::test_get_email_content_not_found", "test_app_consolidated.py::TestAPIEndpoints::test_get_email_content_success", "test_app_consolidated.py::TestAPIEndpoints::test_get_email_history_missing_session", "test_app_consolidated.py::TestAPIEndpoints::test_get_email_history_success", "test_app_consolidated.py::TestAPIEndpoints::test_switch_email_missing_params", "test_app_consolidated.py::TestAPIEndpoints::test_switch_email_not_in_history", "test_app_consolidated.py::TestAPIEndpoints::test_switch_email_success", "test_app_consolidated.py::TestBasicFeatures::test_error_response_basic", "test_app_consolidated.py::TestBasicFeatures::test_error_response_with_details", "test_app_consolidated.py::TestBasicFeatures::test_get_email_cache_key", "test_app_consolidated.py::TestConcurrentAccess::test_concurrent_address_generation", "test_app_consolidated.py::TestConcurrentAccess::test_concurrent_database_operations", "test_app_consolidated.py::TestConfigValidation::test_validate_config_missing_secret_key", "test_app_consolidated.py::TestConfigValidation::test_validate_config_success", "test_app_consolidated.py::TestConfiguration::test_domain_name_missing", "test_app_consolidated.py::TestConfiguration::test_secret_key_missing", "test_app_consolidated.py::TestConfiguration::test_secret_key_too_short", "test_app_consolidated.py::TestDatabaseFunctions::test_get_db_connection", "test_app_consolidated.py::TestDatabaseFunctions::test_init_db_schema", "test_app_consolidated.py::TestErrorHandlers::test_400_error_handler", "test_app_consolidated.py::TestErrorHandlers::test_404_error_handler", "test_app_consolidated.py::TestErrorHandlers::test_429_error_handler", "test_app_consolidated.py::TestErrorHandlers::test_500_error_handler", "test_app_consolidated.py::TestErrorHandlers::test_database_error_handler", "test_app_consolidated.py::TestErrorHandlers::test_value_error_handler", "test_app_consolidated.py::TestFrontendRoutes::test_index_route", "test_app_consolidated.py::TestFrontendRoutes::test_test_custom_route", "test_app_consolidated.py::TestHelperFunctions::test_delete_email_function", "test_app_consolidated.py::TestHelperFunctions::test_get_email_history_function", "test_app_consolidated.py::TestHelperFunctions::test_switch_to_email_function", "test_app_consolidated.py::TestPerformance::test_concurrent_expiration_check", "test_app_consolidated.py::TestPerformance::test_rate_limit_window_reset", "test_app_consolidated.py::TestValidationFunctions::test_sanitize_html_content", "test_app_consolidated.py::TestValidationFunctions::test_validate_custom_prefix_invalid", "test_app_consolidated.py::TestValidationFunctions::test_validate_custom_prefix_valid", "test_app_consolidated.py::TestValidationFunctions::test_validate_email_address_invalid", "test_app_consolidated.py::TestValidationFunctions::test_validate_email_address_valid", "test_app_consolidated.py::TestValidationFunctions::test_validate_timestamp_invalid", "test_app_consolidated.py::TestValidationFunctions::test_validate_timestamp_valid", "test_app_enhanced.py::TestAppEnhanced::test_api_endpoints_with_malformed_requests", "test_app_enhanced.py::TestAppEnhanced::test_api_generate_address_limits", "test_app_enhanced.py::TestAppEnhanced::test_api_generate_address_rate_limiting", "test_app_enhanced.py::TestAppEnhanced::test_api_get_email_content_security", "test_app_enhanced.py::TestAppEnhanced::test_api_get_emails_pagination", "test_app_enhanced.py::TestAppEnhanced::test_api_get_emails_time_filtering", "test_app_enhanced.py::TestAppEnhanced::test_application_startup_sequence", "test_app_enhanced.py::TestAppEnhanced::test_concurrent_address_generation", "test_app_enhanced.py::TestAppEnhanced::test_config_validation_function", "test_app_enhanced.py::TestAppEnhanced::test_database_connection_pooling", "test_app_enhanced.py::TestAppEnhanced::test_database_transaction_integrity", "test_app_enhanced.py::TestAppEnhanced::test_error_response_function", "test_app_enhanced.py::TestAppEnhanced::test_index_page_config_injection", "test_app_enhanced.py::TestAppEnhanced::test_index_page_rendering", "test_app_enhanced.py::TestAppEnhanced::test_memory_usage_with_many_emails", "test_app_enhanced.py::TestAppEnhanced::test_static_file_serving", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_boundary_time_conditions", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_creates_database_directory", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_database_error", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_expired_emails_with_mails", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_expired_emails_without_mails", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_general_exception", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_large_dataset", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_missing_database_path_env", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_no_database_path", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_no_expired_emails", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_transaction_rollback", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_utf8_email_addresses", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_with_database_path_parameter", "test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_with_env_database_path", "test_cleanup_script.py::TestCleanupScript::test_cleanup_all_expired", "test_cleanup_script.py::TestCleanupScript::test_cleanup_creates_database_directory", "test_cleanup_script.py::TestCleanupScript::test_cleanup_database_not_exists", "test_cleanup_script.py::TestCleanupScript::test_cleanup_database_permission_error", "test_cleanup_script.py::TestCleanupScript::test_cleanup_expired_emails_success", "test_cleanup_script.py::TestCleanupScript::test_cleanup_logging_configuration", "test_cleanup_script.py::TestCleanupScript::test_cleanup_no_database_path", "test_cleanup_script.py::TestCleanupScript::test_cleanup_no_expired_emails", "test_cleanup_script.py::TestCleanupScript::test_cleanup_transaction_rollback", "test_cleanup_script.py::TestCleanupScript::test_cleanup_with_environment_variable", "test_cleanup_script.py::TestCleanupScript::test_main_function_execution", "test_cleanup_script.py::TestCleanupScriptLogging::test_custom_log_file", "test_cleanup_script.py::TestCleanupScriptLogging::test_log_directory_creation", "test_cleanup_script.py::TestCleanupScriptLogging::test_logging_configuration", "test_cleanup_script.py::TestCleanupScriptLogging::test_main_script_execution", "test_cleanup_script.py::TestCleanupScriptLogging::test_no_log_file_configuration", "test_cleanup_script.py::TestCleanupScriptMain::test_main_execution", "test_cleanup_script.py::TestCleanupScriptMain::test_script_can_be_imported", "test_custom_prefix.py::test_custom_prefix", "test_custom_prefix.py::test_no_prefix", "test_custom_prefix_feature.py::TestCustomPrefixFeature::test_api_request_validation", "test_custom_prefix_feature.py::TestCustomPrefixFeature::test_custom_prefix_collision_handling", "test_custom_prefix_feature.py::TestCustomPrefixFeature::test_custom_prefix_regex_validation", "test_custom_prefix_feature.py::TestCustomPrefixFeature::test_generate_address_with_invalid_custom_prefix", "test_custom_prefix_feature.py::TestCustomPrefixFeature::test_generate_address_with_valid_custom_prefix", "test_custom_prefix_feature.py::TestCustomPrefixFeature::test_generate_address_without_custom_prefix", "test_custom_prefix_feature.py::TestCustomPrefixFeature::test_generate_random_address_function", "test_custom_prefix_feature.py::TestCustomPrefixIntegration::test_end_to_end_custom_prefix_workflow", "test_integration_e2e.py::TestEndToEndIntegration::test_api_response_format_consistency", "test_integration_e2e.py::TestEndToEndIntegration::test_complete_email_workflow", "test_integration_e2e.py::TestEndToEndIntegration::test_concurrent_operations_workflow", "test_integration_e2e.py::TestEndToEndIntegration::test_database_consistency_after_operations", "test_integration_e2e.py::TestEndToEndIntegration::test_email_expiration_workflow", "test_integration_e2e.py::TestEndToEndIntegration::test_error_handling_workflow", "test_integration_e2e.py::TestEndToEndIntegration::test_mail_handler_integration", "test_integration_e2e.py::TestEndToEndIntegration::test_multiple_emails_workflow", "test_integration_e2e.py::TestEndToEndIntegration::test_rate_limiting_workflow", "test_integration_e2e.py::TestSystemIntegration::test_environment_configuration_integration", "test_integration_e2e.py::TestSystemIntegration::test_logging_integration", "test_mail_handler.py::TestDatabaseOperations::test_store_email_expired", "test_mail_handler.py::TestDatabaseOperations::test_store_email_success", "test_mail_handler.py::TestEmailParsing::test_decode_header_complex", "test_mail_handler.py::TestEmailParsing::test_decode_header_empty", "test_mail_handler.py::TestEmailParsing::test_parse_email_body_clean_text_function", "test_mail_handler.py::TestEmailParsing::test_parse_email_body_with_invalid_charset", "test_mail_handler.py::TestEmailParsing::test_parse_email_body_with_nested_parts", "test_mail_handler.py::TestErrorHandling::test_config_check_in_main", "test_mail_handler.py::TestErrorHandling::test_log_dir_creation_error", "test_mail_handler.py::TestMainFunction::test_main_empty_email", "test_mail_handler.py::TestMainFunction::test_main_missing_recipient", "test_mail_handler.py::TestMainFunction::test_main_successful_processing", "test_mail_handler_enhanced.py::TestEmailBodyParsing::test_parse_email_with_base64_content", "test_mail_handler_enhanced.py::TestEmailBodyParsing::test_parse_email_with_inline_images", "test_mail_handler_enhanced.py::TestEmailBodyParsing::test_parse_html_email", "test_mail_handler_enhanced.py::TestEmailBodyParsing::test_parse_malformed_email", "test_mail_handler_enhanced.py::TestEmailBodyParsing::test_parse_multipart_alternative", "test_mail_handler_enhanced.py::TestEmailBodyParsing::test_parse_nested_multipart", "test_mail_handler_enhanced.py::TestEmailBodyParsing::test_parse_simple_text_email", "test_mail_handler_enhanced.py::TestEmailHeaderDecoding::test_decode_empty_header", "test_mail_handler_enhanced.py::TestEmailHeaderDecoding::test_decode_invalid_encoding", "test_mail_handler_enhanced.py::TestEmailHeaderDecoding::test_decode_malformed_header", "test_mail_handler_enhanced.py::TestEmailHeaderDecoding::test_decode_mixed_encoding_header", "test_mail_handler_enhanced.py::TestEmailHeaderDecoding::test_decode_multiple_encoding_parts", "test_mail_handler_enhanced.py::TestEmailHeaderDecoding::test_decode_quoted_printable_header", "test_mail_handler_enhanced.py::TestEmailHeaderDecoding::test_decode_utf8_header", "test_mail_handler_enhanced.py::TestEmailStorage::test_store_email_database_lock", "test_mail_handler_enhanced.py::TestEmailStorage::test_store_email_disk_full", "test_mail_handler_enhanced.py::TestEmailStorage::test_store_email_unicode_content", "test_mail_handler_enhanced.py::TestEmailStorage::test_store_large_email", "test_mail_handler_enhanced.py::TestMailHandlerEdgeCases::test_decode_header_with_none_parts", "test_mail_handler_enhanced.py::TestMailHandlerEdgeCases::test_main_empty_input", "test_mail_handler_enhanced.py::TestMailHandlerEdgeCases::test_main_invalid_email_format", "test_mail_handler_enhanced.py::TestMailHandlerEdgeCases::test_main_missing_database_config", "test_mail_handler_enhanced.py::TestMailHandlerEdgeCases::test_main_no_recipient_argument", "test_mail_handler_enhanced.py::TestMailHandlerEdgeCases::test_main_store_email_failure", "test_mail_handler_enhanced.py::TestMailHandlerEdgeCases::test_parse_email_body_with_encoding_error", "test_mail_handler_enhanced.py::TestPerformanceAndStress::test_decode_very_long_header", "test_mail_handler_enhanced.py::TestPerformanceAndStress::test_multiple_concurrent_email_parsing", "test_mail_handler_enhanced.py::TestPerformanceAndStress::test_parse_deeply_nested_multipart", "test_polling.py::test_polling"]