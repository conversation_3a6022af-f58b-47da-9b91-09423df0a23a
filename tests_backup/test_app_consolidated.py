"""
整合所有应用核心功能的测试用例。
包括基础功能、高级功能、性能测试等。
"""
import pytest
import os
import sys
import sqlite3
import concurrent.futures
import logging
from unittest import mock
from datetime import datetime, timezone, timedelta
from pathlib import Path
import subprocess
from flask import Flask

from app import (
    error_response, get_email_cache_key, rate_limit, app,
    init_db_schema, get_db_connection, validate_config
)

def clear_rate_limit():
    """清理速率限制存储"""
    if hasattr(rate_limit, "clear"):
        rate_limit.clear()

class TestBasicFeatures:
    """基础功能测试"""

    def test_error_response_basic(self):
        """测试基本错误响应"""
        with app.app_context():
            resp, code = error_response("msg", 400)
            assert code == 400
            assert resp.json["error"] == "msg"

    def test_error_response_with_details(self):
        """测试带详细信息的错误响应"""
        with app.app_context():
            resp, code = error_response("msg", 403, details={"foo": 1})
            assert code == 403
            assert resp.json["details"] == {"foo": 1}

    def test_get_email_cache_key(self):
        """测试邮件缓存键生成"""
        assert get_email_cache_key(1, "<EMAIL>") == "1:<EMAIL>"
        assert get_email_cache_key(123, "x") == "123:x"

class TestConfiguration:
    """配置相关测试"""

    def run_app_in_subprocess(self, env_overrides):
        """在子进程中运行应用"""
        env = os.environ.copy()
        env.update(env_overrides)
        env['PYTHONPATH'] = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        result = subprocess.run([sys.executable, 'app.py'], env=env, capture_output=True, text=True)
        return result

    def test_secret_key_missing(self):
        """测试缺少密钥配置"""
        env = {'FLASK_DEBUG': '0'}
        if 'SECRET_KEY' in os.environ:
            env['SECRET_KEY'] = ''
        result = self.run_app_in_subprocess(env)
        assert result.returncode == 1
        assert 'SECRET_KEY' in result.stdout or 'SECRET_KEY' in result.stderr

    def test_secret_key_too_short(self):
        """测试密钥长度不足"""
        env = {'FLASK_DEBUG': '0', 'SECRET_KEY': 'short'}
        result = self.run_app_in_subprocess(env)
        assert result.returncode == 1
        assert 'SECRET_KEY' in result.stdout or 'SECRET_KEY' in result.stderr

    def test_domain_name_missing(self):
        """测试缺少域名配置"""
        env = {'FLASK_DEBUG': '0', 'SECRET_KEY': 'x'*32}
        if 'DOMAIN_NAME' in os.environ:
            env['DOMAIN_NAME'] = ''
        result = self.run_app_in_subprocess(env)
        assert result.returncode == 1
        assert 'DOMAIN_NAME' in result.stdout or 'DOMAIN_NAME' in result.stderr

class TestConcurrentAccess:
    """并发访问测试"""

    def test_concurrent_address_generation(self, client, app_instance):
        """测试并发生成邮箱地址"""
        clear_rate_limit()
        num_concurrent_requests = 10
        max_allowed = 5
        statuses = []
        addresses = set()

        # 临时禁用测试模式以启用速率限制
        original_testing = app_instance.config['TESTING']
        app_instance.config['TESTING'] = False

        try:
            def make_request():
                response = client.post('/api/generate-address',
                                     json={},
                                     content_type='application/json')
                if response.status_code == 201:
                    result = response.get_json()
                    if result["success"]:
                        return 201, result["data"]["address"]
                return response.status_code, None

            with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent_requests) as executor:
                futures = [executor.submit(make_request) for _ in range(num_concurrent_requests)]
                results = [f.result() for f in concurrent.futures.as_completed(futures)]

            for status, addr in results:
                statuses.append(status)
                if addr:
                    addresses.add(addr)

            assert statuses.count(201) <= max_allowed
            assert statuses.count(201) == len(addresses)
            assert statuses.count(429) >= 0
        finally:
            # 恢复测试模式
            app_instance.config['TESTING'] = original_testing

    def test_concurrent_database_operations(self, app_instance, db_conn):
        """测试并发数据库操作"""
        clear_rate_limit()
        num_concurrent_requests = 10
        db_path = app_instance.config['DATABASE_PATH']

        def insert_and_read():
            now_utc = datetime.now(timezone.utc)
            with sqlite3.connect(db_path) as thread_conn:
                thread_conn.row_factory = sqlite3.Row
                cursor = thread_conn.cursor()
                try:
                    cursor.execute(
                        """
                        INSERT INTO temporary_emails (address, expires_at, created_at)
                        VALUES (?, ?, ?)
                        """,
                        (
                            f"{now_utc.timestamp()}@example.com",
                            now_utc.isoformat(),
                            now_utc.isoformat()
                        )
                    )
                    thread_conn.commit()
                    return True
                except sqlite3.IntegrityError:
                    thread_conn.rollback()
                    return False

        with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent_requests) as executor:
            futures = [executor.submit(insert_and_read) for _ in range(num_concurrent_requests)]
            results = [f.result() for f in concurrent.futures.as_completed(futures)]

        cursor = db_conn.cursor()
        cursor.execute("SELECT COUNT(*) as count FROM temporary_emails WHERE address LIKE '%.%@example.com'")
        total_records = cursor.fetchone()["count"]
        successful_operations = sum(1 for r in results if r)
        assert successful_operations == total_records

        cursor.execute("DELETE FROM temporary_emails WHERE address LIKE '%.%@example.com'")
        db_conn.commit()

class TestPerformance:
    """性能测试"""

    def test_rate_limit_window_reset(self, client, app_instance):
        """测试速率限制窗口重置"""
        clear_rate_limit()
        max_requests = 5
        window_seconds = 60

        # 临时禁用测试模式以启用速率限制
        original_testing = app_instance.config['TESTING']
        app_instance.config['TESTING'] = False

        try:
            # 发送到达限制的请求
            for _ in range(max_requests):
                response = client.post('/api/generate-address',
                                     json={},
                                     content_type='application/json')
            response = client.post('/api/generate-address',
                                 json={},
                                 content_type='application/json')
            assert response.status_code == 429
        finally:
            # 恢复测试模式
            app_instance.config['TESTING'] = original_testing

        # 模拟时间窗口后再请求
        with mock.patch('datetime.datetime') as mock_datetime:
            now = datetime.now(timezone.utc)
            mock_datetime.now.return_value = now + timedelta(seconds=window_seconds + 1)
            mock_datetime.timezone = timezone
            response = client.post('/api/generate-address',
                                 json={},
                                 content_type='application/json')
            assert response.status_code in [201, 500, 429]

    def test_concurrent_expiration_check(self, client, db_conn):
        """测试并发过期检查"""
        clear_rate_limit()
        num_concurrent_requests = 10
        test_addresses = []
        now_utc = datetime.now(timezone.utc)

        # 准备测试数据
        cursor = db_conn.cursor()
        for i in range(num_concurrent_requests):
            address = f"test{i}@example.com"
            expires_at = (now_utc + timedelta(minutes=30)).isoformat()
            cursor.execute(
                "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
                (address, expires_at, now_utc.isoformat())
            )
            test_addresses.append(address)
        db_conn.commit()

        def check_address(addr):
            response = client.get(f'/api/emails/{addr}')
            return response.status_code

        with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent_requests) as executor:
            futures = [executor.submit(check_address, addr) for addr in test_addresses]
            results = [f.result() for f in concurrent.futures.as_completed(futures)]

        # 验证所有请求都返回了有效状态码
        assert all(status in [200, 404] for status in results)

        # 清理测试数据
        cursor.execute("DELETE FROM temporary_emails WHERE address LIKE '<EMAIL>'")
        db_conn.commit()

if __name__ == "__main__":
    pytest.main()