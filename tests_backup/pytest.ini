[pytest]
markers =
    mail_handler_tests: mark tests for mail_handler.py
    mail_handler_additional: mark additional tests for mail_handler.py
    mail_handler_edge_cases: mark edge case tests for mail_handler.py
    mail_handler_coverage: mark coverage tests for mail_handler.py
    mail_handler_final: mark final coverage tests for mail_handler.py
    mail_handler_remaining: mark remaining coverage tests for mail_handler.py
