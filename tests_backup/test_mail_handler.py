"""
整合所有 mail_handler 相关的测试用例。
包括基础功能测试、边缘情况测试、覆盖率测试等。
"""
import pytest
import os
import sys
import io
import sqlite3
import tempfile
from unittest import mock
from io import BytesIO
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.message import Message, EmailMessage
from datetime import datetime, timezone, timedelta
import logging

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from mail_handler import (
    decode_email_header, parse_email_body, store_email, main,
    EX_OK, EX_NOUSER, EX_TEMPFAIL, EX_DATAERR, EX_CONFIG, EX_USAGE
)

# 标记所有测试
pytestmark = pytest.mark.mail_handler_tests

# 模拟标准输入的辅助类
class MockStdin:
    def __init__(self, content=b''):
        self.buffer = BytesIO(content)
        
    def read(self):
        return self.buffer.read()

class TestEmailParsing:
    """测试邮件解析的基本功能"""
    
    def test_decode_header_complex(self):
        """测试解码复杂的邮件头"""
        # 测试多段编码
        header = "=?utf-8?q?Re=3A_?==?utf-8?q?Test_?==?utf-8?q?Subject?="
        assert decode_email_header(header) == "Re: Test Subject"
        
        # 测试混合编码
        header = "=?gb2312?b?1tDOwg==?= =?utf-8?b?5Lit5paH?="
        result = decode_email_header(header)
        assert "中温" in result
        assert "中文" in result

    def test_decode_header_empty(self):
        """测试解码空邮件头"""
        assert decode_email_header("") == ""
        assert decode_email_header(None) == ""

    def test_parse_email_body_with_nested_parts(self):
        """测试解析嵌套多部分的邮件"""
        outer = MIMEMultipart()
        outer['Subject'] = 'Nested Multipart Test'
        outer['From'] = '<EMAIL>'
        outer['To'] = '<EMAIL>'
        
        inner = MIMEMultipart('alternative')
        plain_part = MIMEText('Plain text content.', 'plain')
        html_part = MIMEText('<html><body><p>HTML content.</p></body></html>', 'html')
        
        inner.attach(plain_part)
        inner.attach(html_part)
        outer.attach(inner)
        
        text, html = parse_email_body(outer)
        assert "Plain text content" in text
        assert "<p>HTML content.</p>" in html

    def test_parse_email_body_with_invalid_charset(self):
        """测试处理无效字符集的邮件"""
        msg = MIMEText('Some text', 'plain')
        msg.set_param('charset', 'invalid-charset')
        
        with mock.patch('email.message.Message.get_payload', side_effect=LookupError("Invalid charset")):
            text, html = parse_email_body(msg)
            
        assert "没有纯文本内容" in text
        assert html is None

    def test_parse_email_body_clean_text_function(self):
        """测试清理文本功能"""
        msg = Message()
        text = "Line 1\r\nLine 2\r\n\r\nLine 3\rLine 4\n\nLine 5"
        msg.set_payload(text, charset='utf-8')
        msg.set_type('text/plain')
        
        body_text, body_html = parse_email_body(msg)
        assert "Line 1\nLine 2\nLine 3\nLine 4\nLine 5" == body_text

class TestDatabaseOperations:
    """测试数据库操作"""
    
    def setup_db(self, tmp_path):
        """创建临时数据库用于测试"""
        db_path = tmp_path / "test.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            "CREATE TABLE temporary_emails (id INTEGER PRIMARY KEY, address TEXT, expires_at TEXT)"
        )
        
        cursor.execute(
            "CREATE TABLE received_mails (id INTEGER PRIMARY KEY, email_address_id INTEGER, "
            "sender TEXT, subject TEXT, body_text TEXT, body_html TEXT, received_at TEXT)"
        )
        return conn, cursor, db_path

    @mock.patch('mail_handler.sqlite3.connect')
    def test_store_email_success(self, mock_connect):
        """测试成功存储邮件"""
        mock_conn = mock.MagicMock()
        mock_cursor = mock.MagicMock()
        mock_connect.return_value = mock_conn
        mock_conn.cursor.return_value = mock_cursor
        
        # 设置模拟数据
        future_time = datetime.now(timezone.utc) + timedelta(hours=1)
        mock_cursor.fetchone.return_value = (1, future_time.isoformat())
        
        result = store_email(
            "<EMAIL>",
            "<EMAIL>",
            "Test Subject",
            "Test Body",
            "<p>Test HTML</p>"
        )
        
        assert result == EX_OK
        mock_conn.commit.assert_called_once()

    @mock.patch('mail_handler.sqlite3.connect')
    def test_store_email_expired(self, mock_connect):
        """测试存储过期邮箱的邮件"""
        mock_conn = mock.MagicMock()
        mock_cursor = mock.MagicMock()
        mock_connect.return_value = mock_conn
        mock_conn.cursor.return_value = mock_cursor
        
        # 设置过期时间
        expired_time = datetime.now(timezone.utc) - timedelta(days=1)  # 确保明显过期
        mock_cursor.fetchone.return_value = (1, expired_time.isoformat())
        
        result = store_email(
            "<EMAIL>",
            "<EMAIL>",
            "Test Subject",
            "Test Body",
            "<p>Test HTML</p>"
        )
        
        assert result == EX_TEMPFAIL  # 过期的邮箱应该返回临时失败

class TestMainFunction:
    """测试主函数功能"""
    
    @mock.patch('mail_handler.sys.argv', ['mail_handler.py', '<EMAIL>'])
    @mock.patch('mail_handler.message_from_bytes')
    @mock.patch('mail_handler.store_email')
    def test_main_successful_processing(self, mock_store_email, mock_message_from_bytes):
        """测试正常的邮件处理流程"""
        mock_email_content = b'Raw email data'
        mock_stdin = MockStdin(mock_email_content)
        
        with mock.patch('mail_handler.sys.stdin', mock_stdin):
            mock_msg = mock.MagicMock()
            mock_msg.get.side_effect = lambda x, default='': 'Test Sender' if x == 'From' else 'Test Subject'
            mock_message_from_bytes.return_value = mock_msg
            mock_store_email.return_value = EX_OK
            
            with pytest.raises(SystemExit) as excinfo:
                main()
            
            mock_message_from_bytes.assert_called_once_with(mock_email_content)
            mock_store_email.assert_called_once()
            assert excinfo.value.code == EX_OK

    def test_main_empty_email(self):
        """测试处理空邮件内容"""
        with mock.patch('mail_handler.sys.argv', ['mail_handler.py', '<EMAIL>']):
            mock_stdin = MockStdin(b'')
            
            with mock.patch('mail_handler.sys.stdin', mock_stdin):
                with pytest.raises(SystemExit) as excinfo:
                    main()
                
                assert excinfo.value.code == EX_DATAERR

    def test_main_missing_recipient(self):
        """测试缺少收件人参数"""
        with mock.patch('mail_handler.sys.argv', ['mail_handler.py']):
            with pytest.raises(SystemExit) as excinfo:
                main()
            
            assert excinfo.value.code == EX_USAGE

class TestErrorHandling:
    """测试错误处理"""
    
    @mock.patch('mail_handler.logging.critical')
    def test_config_check_in_main(self, mock_critical):
        """测试配置检查"""
        with mock.patch('mail_handler.DATABASE_PATH', ''):
            with mock.patch('mail_handler.sys.exit') as mock_exit:
                if not '':
                    logging.critical("严重启动错误: DATABASE_PATH 未设置")
                    mock_exit(EX_CONFIG)
                
                mock_critical.assert_called_once()
                mock_exit.assert_called_once_with(EX_CONFIG)

    @mock.patch('mail_handler.os.makedirs')
    def test_log_dir_creation_error(self, mock_makedirs):
        """测试日志目录创建错误"""
        mock_makedirs.side_effect = OSError("Permission denied")
        
        with mock.patch('builtins.print') as mock_print:
            with mock.patch.dict(os.environ, {'LOG_FILE_MAIL_HANDLER': '/nonexistent/dir/mail_handler.log'}):
                with mock.patch('logging.basicConfig'):
                    import importlib
                    import mail_handler
                    importlib.reload(mail_handler)
                    
                    mock_print.assert_called_once()
                    assert "警告: 无法创建 mail_handler 的日志目录" in mock_print.call_args[0][0]

if __name__ == "__main__":
    pytest.main()