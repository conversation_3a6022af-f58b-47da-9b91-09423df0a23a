import os
import sys
import time
import sqlite3
import logging
import argparse
import requests
from pathlib import Path
from datetime import datetime, timezone, timedelta
from dotenv import load_dotenv, find_dotenv

# 加载环境变量
# 先尝试加载常见路径的 .env 文件
env_paths = [
    # 优先加载测试目录下的 .env 文件
    Path(__file__).resolve().parent / '.env',
    # 其次加载项目根目录下的 .env 文件
    Path(__file__).resolve().parent.parent / '.env',
]

env_loaded = False
for env_path in env_paths:
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
        print(f"已加载环境变量文件: {env_path}")
        env_loaded = True
        break

# 如果没有找到指定路径的 .env 文件，尝试自动查找
if not env_loaded:
    dotenv_path = find_dotenv()
    if dotenv_path:
        load_dotenv(dotenv_path=dotenv_path)
        print(f"已加载环境变量文件: {dotenv_path}")
    else:
        print("警告: 未找到 .env 文件，将使用默认配置或环境变量")

# 初始化基本日志配置
# 稍后会在获取完整配置后重新配置日志级别
logging.basicConfig(
    level=logging.INFO,  # 初始默认级别，稍后会更新
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test-polling-production')

def configure_logging(log_level_name):
    """根据配置设置日志级别"""
    # 将日志级别名称转换为实际的日志级别
    try:
        log_level = getattr(logging, log_level_name.upper(), logging.INFO)
        logger.setLevel(log_level)
        # 同时更新根日志记录器
        logging.getLogger().setLevel(log_level)
        logger.debug(f"日志级别设置为: {log_level_name.upper()}")
    except (AttributeError, TypeError) as e:
        logger.warning(f"无效的日志级别 '{log_level_name}'，使用默认值 INFO: {e}")
        logger.setLevel(logging.INFO)

# 默认配置，可通过 .env 文件或命令行参数覆盖
DEFAULT_CONFIG = {
    "API_HOST": os.getenv('API_HOST', 'http://127.0.0.1'),
    "API_PORT": os.getenv('API_PORT', '5000'),
    "DATABASE_PATH": os.getenv('DATABASE_PATH', None),  # 默认会在脚本中自动检测
    "SKIP_API_CHECK": os.getenv('SKIP_API_CHECK', 'false').lower() == 'true'
}


def parse_args():
    """解析命令行参数并与环境变量合并"""
    parser = argparse.ArgumentParser(description='邮件功能生产环境测试工具')
    parser.add_argument('--api-host', dest='API_HOST', 
                      help=f'API主机地址 (默认: {DEFAULT_CONFIG["API_HOST"]})')
    parser.add_argument('--api-port', dest='API_PORT', 
                      help=f'API端口 (默认: {DEFAULT_CONFIG["API_PORT"]})')
    parser.add_argument('--db', dest='DATABASE_PATH', 
                      help='数据库完整路径 (默认自动检测)')
    parser.add_argument('--skip-api', dest='SKIP_API_CHECK', action='store_true',
                      help='跳过API检查，只测试数据库部分')
    parser.add_argument('--log-level', dest='LOG_LEVEL', 
                      choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                      help='日志级别 (默认: INFO)')
    
    args = parser.parse_args()
    # 合并默认配置和命令行参数
    config = DEFAULT_CONFIG.copy()
    
    # 将命令行参数应用到配置中（只有非None的值）
    for key, value in vars(args).items():
        if value is not None:
            # 特殊处理布尔类型
            if key == 'SKIP_API_CHECK' and value is True:
                config[key] = True
            elif value:  # 其他非空值
                config[key] = value
    
    # 验证配置
    if not config['API_HOST'].startswith(('http://', 'https://')):
        config['API_HOST'] = f"http://{config['API_HOST']}"
    
    return config


def locate_database_path():
    """自动定位数据库文件路径"""
    # 首先检查环境变量中是否有数据库路径（这应该已经在 DEFAULT_CONFIG 中处理了，但为了保险起见再检查一次）
    db_env_path = os.getenv('DATABASE_PATH')
    if db_env_path:
        db_path = Path(db_env_path)
        if db_path.exists():
            logger.info(f"通过环境变量找到数据库: {db_path}")
            return str(db_path)
        else:
            logger.warning(f"环境变量中的数据库路径不存在: {db_path}")
    
    # 检查常见的数据库位置
    common_locations = [
        # 当前目录
        Path.cwd() / 'database' / 'tempmail.db',
        # 项目根目录
        Path(__file__).resolve().parent.parent / 'database' / 'tempmail.db',
        # tests目录下的database
        Path(__file__).resolve().parent / 'database' / 'tempmail.db',
        # /var/www/tempmail 目录 (常见的生产环境路径)
        Path('/var/www/tempmail/database/tempmail.db')
    ]
    
    for db_path in common_locations:
        if db_path.exists():
            logger.info(f"找到数据库: {db_path}")
            return str(db_path)
    
    logger.error("无法自动定位数据库，请使用 --db 参数指定数据库路径或在 .env 文件中设置 DATABASE_PATH")
    sys.exit(1)


def get_current_email(db_path):
    """从数据库中获取一个未过期的、最近创建的邮箱地址"""
    try:
        db_conn = sqlite3.connect(db_path)
        db_cursor = db_conn.cursor()
        
        # 获取未过期的最新邮箱
        now_utc = datetime.now(timezone.utc).isoformat()
        db_cursor.execute(
            "SELECT id, address, expires_at FROM temporary_emails WHERE expires_at > ? ORDER BY created_at DESC LIMIT 1",
            (now_utc,)
        )
        
        row = db_cursor.fetchone()
        
        if not row:
            # 如果没有找到有效的邮箱，创建一个新的测试邮箱
            # 这里我们只是用于测试，所以直接在数据库创建一个临时邮箱
            test_address = f"test_{int(time.time())}@test.local"
            expires_at = (datetime.now(timezone.utc) + timedelta(hours=24)).isoformat()
            created_at = datetime.now(timezone.utc).isoformat()
            
            db_cursor.execute(
                "INSERT INTO temporary_emails (address, created_at, expires_at) VALUES (?, ?, ?)",
                (test_address, created_at, expires_at)
            )
            db_conn.commit()
            
            # 获取新插入的ID
            db_cursor.execute("SELECT last_insert_rowid()")
            email_id = db_cursor.fetchone()[0]
            
            logger.info(f"在数据库中创建了新的测试邮箱: {test_address} (ID: {email_id})")
            db_conn.close()
            return email_id, test_address
            
        email_id, email_address, expires_at = row
        expiry_time = datetime.fromisoformat(expires_at)
        now = datetime.now(timezone.utc)
        hours_left = (expiry_time - now).total_seconds() / 3600
        
        logger.info(f"获取到有效邮箱: {email_address} (ID: {email_id}, 剩余有效时间: {hours_left:.1f}小时)")
        db_conn.close()
        return email_id, email_address
    
    except sqlite3.Error as e:
        logger.error(f"数据库错误: {e}")
        raise


def send_test_mail(db_path, email_id, count=5):
    """直接在数据库中插入测试邮件"""
    try:
        db_conn = sqlite3.connect(db_path)
        db_cursor = db_conn.cursor()
        
        # 先检查该邮箱是否存在
        db_cursor.execute("SELECT address FROM temporary_emails WHERE id = ?", (email_id,))
        email_row = db_cursor.fetchone()
        if not email_row:
            logger.error(f"邮箱ID {email_id} 在数据库中不存在")
            db_conn.close()
            return False
        
        # 插入测试邮件
        inserted_count = 0
        for i in range(count):
            now = datetime.now(timezone.utc)
            subject = f"生产环境测试邮件 #{i+1}"
            body = f"这是一封生产环境测试邮件，编号 {i+1}，发送时间: {now.strftime('%Y-%m-%d %H:%M:%S %Z')}"
            
            db_cursor.execute(
                """
                INSERT INTO received_mails 
                (email_address_id, sender, subject, body_text, body_html, received_at) 
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                (email_id, '<EMAIL>', subject, body, f"<p>{body}</p>", now.isoformat())
            )
            inserted_count += 1
            logger.info(f"插入测试邮件 #{i+1}")
            time.sleep(0.2)  # 短暂延迟以区分邮件接收时间
            
        db_conn.commit()
        logger.info(f"成功插入 {inserted_count} 封测试邮件")
        db_conn.close()
        return inserted_count
    
    except sqlite3.Error as e:
        logger.error(f"插入测试邮件时数据库错误: {e}")
        raise


def verify_mails_in_db(db_path, email_id, expected_count):
    """验证数据库中的邮件数量"""
    try:
        db_conn = sqlite3.connect(db_path)
        db_cursor = db_conn.cursor()
        
        # 查询特定发件人发给特定邮箱的邮件
        db_cursor.execute(
            """
            SELECT COUNT(*) FROM received_mails 
            WHERE email_address_id = ? AND sender = '<EMAIL>'
            """, 
            (email_id,)
        )
        
        count = db_cursor.fetchone()[0]
        db_conn.close()
        
        if count >= expected_count:
            logger.info(f"数据库验证成功: 找到 {count} 封测试邮件(预期至少 {expected_count} 封)")
            return True
        else:
            logger.error(f"数据库验证失败: 只找到 {count} 封测试邮件(预期至少 {expected_count} 封)")
            return False
            
    except sqlite3.Error as e:
        logger.error(f"验证邮件时数据库错误: {e}")
        raise


def check_api_connection(config, email_address):
    """检查API连接是否正常工作"""
    if config['SKIP_API_CHECK']:
        logger.info("已跳过API检查")
        return True
    
    # 构建API URL，更智能地处理端口
    api_host = config['API_HOST'].rstrip('/')
    if ':' in api_host:  # 如果主机地址已经包含端口
        api_url = f"{api_host}/api/emails"
    else:
        api_url = f"{api_host}:{config['API_PORT']}/api/emails"
    
    try:
        # 尝试连接API
        logger.info(f"尝试连接 API: {api_url}")
        resp = requests.get(
            api_url, 
            params={"address": email_address}, 
            timeout=5
        )
        
        if resp.ok:
            data = resp.json()
            if data.get("success"):
                emails_count = len(data.get("data", {}).get("emails", []))
                logger.info(f"API连接成功: 返回 {emails_count} 封邮件")
                return True
            else:
                logger.error(f"API请求成功但返回错误: {data.get('error')}")
        else:
            logger.error(f"API请求失败: HTTP {resp.status_code} - {resp.reason}")
        
        return False
        
    except requests.exceptions.ConnectionError as e:
        logger.error(f"API连接错误 - 请确认API服务是否正在运行以及网络是否可达: {e}")
        return False
    except requests.exceptions.Timeout:
        logger.error(f"API请求超时 - 服务响应时间过长")
        return False
    except Exception as e:
        logger.error(f"API请求过程中出现未知错误: {e}")
        return False


def run_tests(config):
    """运行所有测试"""
    # 如果没有指定数据库路径，自动查找
    if not config['DATABASE_PATH']:
        config['DATABASE_PATH'] = locate_database_path()
    
    # 测试步骤
    try:
        # 1. 获取可用邮箱
        logger.info("步骤1: 获取测试用邮箱")
        email_id, email_address = get_current_email(config['DATABASE_PATH'])
        
        # 2. 插入测试邮件
        logger.info("步骤2: 插入测试邮件")
        test_mail_count = send_test_mail(config['DATABASE_PATH'], email_id, count=5)
        
        # 3. 验证数据库中的邮件
        logger.info("步骤3: 验证数据库中的邮件")
        db_verification = verify_mails_in_db(config['DATABASE_PATH'], email_id, test_mail_count)
        
        # 4. 检查API连接 (可选)
        logger.info("步骤4: 检查API连接")
        api_verification = check_api_connection(config, email_address)
        
        # 输出总结
        logger.info("\n========== 测试结果汇总 ==========")
        logger.info(f"数据库测试: {'通过 ✓' if db_verification else '失败 ✗'}")
        
        if config['SKIP_API_CHECK']:
            logger.info("API测试: 已跳过")
        else:
            logger.info(f"API测试: {'通过 ✓' if api_verification else '失败 ✗'}")
            
        # 测试信息
        logger.info("\n========= 测试信息 =========")
        logger.info("测试邮件已发送到数据库:")
        logger.info(f"邮箱地址: {email_address}")
        logger.info(f"邮件数量: {test_mail_count}")
        logger.info("==============================\n")
        
        # 返回整体测试状态
        if not config['SKIP_API_CHECK']:
            return db_verification and api_verification
        return db_verification
        
    except Exception as e:
        logger.error(f"测试过程中出现未处理的异常: {e}")
        return False


def main():
    """主函数"""
    # 获取配置
    config = parse_args()
    
    # 配置日志级别
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    # 如果命令行有指定，则使用命令行设置的日志级别
    if 'LOG_LEVEL' in config:
        log_level = config['LOG_LEVEL']
    configure_logging(log_level)

    logger.info("===== 临时邮箱系统生产环境测试 =====")
    
    # 显示配置信息
    logger.info("测试配置:")
    logger.info(f"- 数据库路径: {config['DATABASE_PATH'] or '自动检测'} (来源: {'环境变量' if os.getenv('DATABASE_PATH') else '默认值'})")
    
    # 更智能地显示API地址
    api_host = config['API_HOST']
    if ':' in api_host and not (api_host.startswith('http://') or api_host.startswith('https://')):
        logger.info(f"- API地址: {api_host} (来源: {'环境变量' if os.getenv('API_HOST') else '默认值'})")
    else:
        api_port = config['API_PORT']
        logger.info(f"- API地址: {api_host}:{api_port} (来源: {'环境变量' if os.getenv('API_HOST') and os.getenv('API_PORT') else '默认值或部分环境变量'})")
    
    logger.info(f"- 跳过API检查: {config['SKIP_API_CHECK']} (来源: {'环境变量' if os.getenv('SKIP_API_CHECK') else '默认值或命令行参数'})")
    logger.info(f"- 日志级别: {log_level} (来源: {'环境变量' if os.getenv('LOG_LEVEL') else ('命令行' if 'LOG_LEVEL' in config else '默认值')})")
    
    # 运行测试
    success = run_tests(config)
    
    # 返回码
    if success:
        logger.info("测试完成: 所有测试通过 ✓")
        sys.exit(0)
    else:
        logger.error("测试完成: 部分测试失败 ✗")
        sys.exit(1)


if __name__ == "__main__":
    main()
