"""
整合所有 API 相关的测试用例。
包括邮箱地址生成、邮件内容检索等功能测试。
"""
import json
import pytest
from datetime import datetime, timedelta, timezone
from unittest import mock
import logging
import sqlite3

class TestEmailGeneration:
    """测试邮箱地址生成功能"""

    def test_generate_address_success(self, client, app_instance, db_conn):
        """测试成功生成新的临时邮箱地址"""
        response = client.post('/api/generate-address',
                             json={},
                             content_type='application/json')

        assert response.status_code == 201

        result = response.get_json()
        assert result["success"] is True
        assert "data" in result

        data = result["data"]
        assert "address" in data
        assert "expires_at" in data

        assert app_instance.config['DOMAIN_NAME'] in data["address"]

        try:
            expires_dt_str = data["expires_at"]
            if expires_dt_str.endswith('Z'):
                expires_dt = datetime.fromisoformat(expires_dt_str)
            else:
                expires_dt = datetime.fromisoformat(expires_dt_str.replace('Z', '+00:00'))

            expiration_hours = app_instance.config.get('EMAIL_EXPIRATION_HOURS', 1)
            now_utc = datetime.now(timezone.utc)
            expected_expires_approx = now_utc + timedelta(hours=expiration_hours)

            assert abs((expires_dt - expected_expires_approx).total_seconds()) < 10
        except ValueError as e:
            pytest.fail(f"expires_at ('{data['expires_at']}') 格式无效或与预期时间差异过大: {e}")

        cursor = db_conn.cursor()
        cursor.execute("SELECT address, expires_at FROM temporary_emails WHERE address = ?", (data["address"],))
        record = cursor.fetchone()

        assert record is not None
        assert record["address"] == data["address"]
        assert record["expires_at"] == data["expires_at"]

    def test_generate_address_api_rate_limited(self, client, app_instance):
        """测试域名未配置时的异常情况"""
        original_domain_name = app_instance.config['DOMAIN_NAME']
        app_instance.config['DOMAIN_NAME'] = None

        response = client.post('/api/generate-address',
                             json={},
                             content_type='application/json')
        assert response.status_code == 503
        data = response.get_json()
        assert "error" in data
        assert "域名未设置" in data["error"]

        app_instance.config['DOMAIN_NAME'] = original_domain_name

    def test_generate_address_retry_mechanism(self, client, app_instance, db_conn):
        """测试地址生成的重试机制"""
        domain_name = app_instance.config['DOMAIN_NAME']
        existing_random = "aaaaaa"
        existing_email = f"{existing_random}@{domain_name}"

        now_utc = datetime.now(timezone.utc)
        expires_at_dt = now_utc + timedelta(hours=1)
        expires_at_iso = expires_at_dt.isoformat()

        cursor = db_conn.cursor()
        cursor.execute(
            "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
            (existing_email, expires_at_iso, now_utc.isoformat())
        )
        db_conn.commit()

        with mock.patch('secrets.token_hex', side_effect=["aaaaaa", "bbbbbb"]):
            response = client.post('/api/generate-address',
                                 json={},
                                 content_type='application/json')

        assert response.status_code == 201
        result = response.get_json()
        assert result["success"] is True
        data = result["data"]
        assert data["address"] == f"bbbbbb@{domain_name}"

    def test_generate_address_all_retries_failed(self, client, app_instance, db_conn):
        """测试当所有重试都失败时的情况"""
        cursor = db_conn.cursor()
        cursor.execute("DELETE FROM temporary_emails WHERE address LIKE 'aaaaaa%'")
        db_conn.commit()

        # 为每个可能的随机值预填充数据库
        domain_name = app_instance.config['DOMAIN_NAME']
        now_utc = datetime.now(timezone.utc)
        expires_at_dt = now_utc + timedelta(hours=1)
        expires_at_iso = expires_at_dt.isoformat()

        # 模拟所有可能的尝试都已存在
        test_addresses = [
            "aaaaaa@" + domain_name,
            "aaaaab@" + domain_name,
            "aaaaac@" + domain_name,
            "aaaaad@" + domain_name,
            "aaaaae@" + domain_name,
            "aaaaaf@" + domain_name,  # 多添加一个以确保超过最大重试次数
        ]

        for address in test_addresses:
            cursor.execute(
                "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
                (address, expires_at_iso, now_utc.isoformat())
            )
        db_conn.commit()

        with mock.patch('secrets.token_hex', side_effect=['aaaaaa', 'aaaaab', 'aaaaac', 'aaaaad', 'aaaaae', 'aaaaaf']):
            response = client.post('/api/generate-address',
                                 json={},
                                 content_type='application/json')
            assert response.status_code == 500  # 系统无法生成唯一地址
            result = response.get_json()
            assert result["success"] is False
            assert "error" in result
            # 由于新的API格式可能不包含details字段，我们移除这个检查
            # assert result["details"]["retry_count"] == 5  # 确认达到最大重试次数
            data = response.get_json()
            assert "error" in data


class TestEmailRetrieval:
    """测试邮件检索功能"""

    def test_get_emails_for_address(self, client, app_instance, db_conn):
        """测试获取指定地址的邮件"""
        # 准备测试数据
        test_address = "test123@" + app_instance.config['DOMAIN_NAME']
        now_utc = datetime.now(timezone.utc)
        expires_at = (now_utc + timedelta(days=1)).isoformat()  # 确保邮箱在测试期间不会过期

        # 插入测试邮箱
        cursor = db_conn.cursor()
        # 删除可能存在的旧数据
        cursor.execute("DELETE FROM temporary_emails WHERE address = ?", (test_address,))
        cursor.execute(
            "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
            (test_address, expires_at, now_utc.isoformat())
        )
        email_id = cursor.lastrowid

        # 插入测试邮件
        cursor.execute(
            """
            INSERT INTO received_mails
            (email_address_id, sender, subject, body_text, body_html, received_at)
            VALUES (?, ?, ?, ?, ?, ?)
            """,
            (email_id, "<EMAIL>", "Test Subject", "Test Body", "<p>Test HTML</p>", now_utc.isoformat())
        )
        db_conn.commit()

        # 测试 API
        response = client.get(f'/api/emails?address={test_address}')
        assert response.status_code == 200

        result = response.get_json()
        assert result["success"] is True
        assert "data" in result

        data = result["data"]
        assert "emails" in data
        emails = data["emails"]
        assert len(emails) == 1

        email = emails[0]
        assert email["sender"] == "<EMAIL>"
        assert email["subject"] == "Test Subject"
        assert "Test Body" in email["summary"]  # API 返回前100个字符作为摘要

    def test_get_emails_invalid_address(self, client):
        """测试获取无效地址的邮件"""
        response = client.get('/api/emails?address=<EMAIL>')
        assert response.status_code == 404

    def test_get_emails_expired_address(self, client, app_instance, db_conn):
        """测试获取已过期地址的邮件"""
        test_address = "expired@" + app_instance.config['DOMAIN_NAME']
        now_utc = datetime.now(timezone.utc)
        expires_at = (now_utc - timedelta(hours=2)).isoformat()  # 已过期

        cursor = db_conn.cursor()
        cursor.execute(
            "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
            (test_address, expires_at, now_utc.isoformat())
        )
        db_conn.commit()

        response = client.get(f'/api/emails?address={test_address}')
        assert response.status_code == 410  # Gone - 资源不再可用

if __name__ == "__main__":
    pytest.main()