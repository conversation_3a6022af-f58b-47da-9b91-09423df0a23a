#!/usr/bin/env python3
"""
最终验证脚本：完整模拟用户报告的问题场景
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def simulate_original_issue():
    """模拟用户报告的原始问题场景"""
    print("🔍 模拟用户报告的原始问题场景")
    print("=" * 50)
    
    # 使用一个新的session来模拟用户的完整流程
    session_id = f"final_test_{int(time.time())}"
    print(f"📱 用户session: {session_id}")
    
    print("\n📝 用户操作步骤:")
    print("1. 生成第一个邮箱：<EMAIL>")
    print("2. 生成第二个邮箱：<EMAIL>")
    print("3. 点击History按钮查看历史记录")
    print("4. 检查是否只显示最新邮箱（bug）还是显示所有邮箱（修复后）")
    
    # 步骤1: 生成第一个邮箱
    print(f"\n🔧 步骤1: 生成第一个邮箱...")
    email1 = generate_email(session_id, "5d483d7d0e54")
    if email1:
        print(f"✅ 第一个邮箱生成成功: {email1}")
    else:
        print("❌ 第一个邮箱生成失败")
        return False
    
    # 步骤2: 生成第二个邮箱
    print(f"\n🔧 步骤2: 生成第二个邮箱...")
    email2 = generate_email(session_id, "Halloworld-2019")
    if email2:
        print(f"✅ 第二个邮箱生成成功: {email2}")
    else:
        print("❌ 第二个邮箱生成失败")
        return False
    
    # 步骤3: 点击History按钮
    print(f"\n🔧 步骤3: 点击History按钮...")
    history = get_history(session_id)
    
    if history is None:
        print("❌ 无法获取历史记录")
        return False
    
    print(f"📋 历史记录获取成功，共 {len(history)} 个邮箱:")
    for i, item in enumerate(history, 1):
        status = "🟢 活跃" if item.get("is_active") else "⚪ 非活跃"
        expired = "⏰ 已过期" if item.get("is_expired") else "✅ 未过期"
        exists = "📧 存在" if item.get("exists_in_db") else "🗑️ 已删除"
        print(f"  {i}. {item.get('email_address')} - {status}, {expired}, {exists}")
    
    # 步骤4: 验证修复效果
    print(f"\n🔧 步骤4: 验证修复效果...")
    
    expected_emails = [email1, email2]
    actual_emails = [item.get('email_address') for item in history]
    
    # 检查所有邮箱是否都在历史记录中
    all_emails_present = all(email in actual_emails for email in expected_emails)
    
    # 检查最新邮箱是否为活跃状态
    latest_email_active = len(history) > 0 and history[0].get('is_active', False)
    
    # 检查历史记录数量是否正确
    correct_count = len(history) == len(expected_emails)
    
    print(f"\n📊 验证结果:")
    print(f"  ✅ 所有邮箱都在历史记录中: {all_emails_present}")
    print(f"  ✅ 最新邮箱为活跃状态: {latest_email_active}")
    print(f"  ✅ 历史记录数量正确: {correct_count}")
    
    if all_emails_present and latest_email_active and correct_count:
        print(f"\n🎉 修复验证成功！")
        print(f"✅ Bug已修复：History功能现在显示所有邮箱，而不是只显示最新的")
        print(f"✅ 用户现在可以看到完整的邮箱历史记录")
        return True
    else:
        print(f"\n❌ 修复验证失败！")
        print(f"期望邮箱: {expected_emails}")
        print(f"实际邮箱: {actual_emails}")
        return False

def test_cross_browser_session_scenario():
    """测试跨浏览器会话场景（模拟关闭浏览器后重新打开）"""
    print(f"\n🌐 测试跨浏览器会话场景")
    print("=" * 50)
    
    # 使用一个已存在的session（模拟localStorage持久化）
    existing_session = "session_1748522407167_igic2h8tk"
    print(f"📱 使用现有session（模拟localStorage）: {existing_session}")
    
    # 获取现有历史记录
    print(f"\n🔧 获取现有历史记录...")
    history_before = get_history(existing_session)
    if history_before:
        print(f"📋 现有历史记录: {len(history_before)} 个邮箱")
        for i, item in enumerate(history_before, 1):
            status = "🟢 活跃" if item.get("is_active") else "⚪ 非活跃"
            print(f"  {i}. {item.get('email_address')} - {status}")
    else:
        print("❌ 无法获取现有历史记录")
        return False
    
    # 模拟用户重新打开浏览器后生成新邮箱
    print(f"\n🔧 模拟重新打开浏览器后生成新邮箱...")
    new_email = generate_email(existing_session, "after-browser-reopen")
    if new_email:
        print(f"✅ 新邮箱生成成功: {new_email}")
    else:
        print("❌ 新邮箱生成失败")
        return False
    
    # 获取更新后的历史记录
    print(f"\n🔧 获取更新后的历史记录...")
    history_after = get_history(existing_session)
    if history_after:
        print(f"📋 更新后历史记录: {len(history_after)} 个邮箱")
        for i, item in enumerate(history_after, 1):
            status = "🟢 活跃" if item.get("is_active") else "⚪ 非活跃"
            print(f"  {i}. {item.get('email_address')} - {status}")
        
        # 验证历史记录是否正确累积
        if len(history_after) == len(history_before) + 1:
            print(f"\n✅ 历史记录正确累积，新邮箱已添加到现有历史中")
            print(f"✅ 跨浏览器会话测试通过")
            return True
        else:
            print(f"\n❌ 历史记录累积异常")
            return False
    else:
        print("❌ 无法获取更新后的历史记录")
        return False

def generate_email(session_id, custom_prefix):
    """生成邮箱"""
    url = f"{BASE_URL}/api/generate-address"
    data = {
        "session_id": session_id,
        "custom_prefix": custom_prefix
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 201:
            result = response.json()
            if result.get("success"):
                return result.get("data", {}).get("address")
        print(f"生成邮箱失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"生成邮箱异常: {e}")
    
    return None

def get_history(session_id):
    """获取历史记录"""
    url = f"{BASE_URL}/api/email-history"
    params = {"session_id": session_id}
    
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                return result.get("data", {}).get("history", [])
        print(f"获取历史记录失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"获取历史记录异常: {e}")
    
    return None

def main():
    print("🚀 开始最终验证...")
    print("🎯 目标：验证History功能bug已完全修复")
    
    # 测试1: 模拟原始问题场景
    test1_passed = simulate_original_issue()
    
    # 测试2: 跨浏览器会话场景
    test2_passed = test_cross_browser_session_scenario()
    
    # 最终总结
    print(f"\n" + "=" * 60)
    print(f"🏁 最终验证结果")
    print(f"=" * 60)
    print(f"原始问题场景测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"跨浏览器会话测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉🎉🎉 所有测试通过！History功能bug修复成功！ 🎉🎉🎉")
        print(f"")
        print(f"✅ 修复总结:")
        print(f"  • 用户现在可以看到所有生成的邮箱历史记录")
        print(f"  • 历史记录在页面刷新后保持完整")
        print(f"  • 已删除或过期的邮箱正确标记并显示删除线")
        print(f"  • Session管理从sessionStorage改为localStorage，确保持久性")
        print(f"")
        print(f"🐛 原始问题: 只显示最新生成的邮箱")
        print(f"✅ 修复后: 显示所有历史邮箱，按时间排序")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
