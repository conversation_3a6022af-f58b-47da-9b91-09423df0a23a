#!/usr/bin/env python3
"""
API配置验证脚本
验证API配置改进是否正常工作
"""

import os
import sys
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

def test_flask_config():
    """测试Flask应用配置"""
    print("🔧 测试Flask应用配置...")
    
    try:
        import app
        flask_app = app.app
        
        # 检查新的API配置是否存在
        api_configs = [
            'API_BASE_URL',
            'API_TIMEOUT', 
            'API_RETRY_ATTEMPTS',
            'API_RETRY_DELAY'
        ]
        
        missing_configs = []
        for config in api_configs:
            if config not in flask_app.config:
                missing_configs.append(config)
        
        if missing_configs:
            print(f"❌ 缺少配置: {', '.join(missing_configs)}")
            return False
        
        print("✅ Flask应用配置验证通过")
        print(f"   - API_BASE_URL: '{flask_app.config['API_BASE_URL']}'")
        print(f"   - API_TIMEOUT: {flask_app.config['API_TIMEOUT']}")
        print(f"   - API_RETRY_ATTEMPTS: {flask_app.config['API_RETRY_ATTEMPTS']}")
        print(f"   - API_RETRY_DELAY: {flask_app.config['API_RETRY_DELAY']}")
        return True
        
    except Exception as e:
        print(f"❌ Flask应用配置测试失败: {e}")
        return False

def test_static_files():
    """测试静态文件是否存在"""
    print("\n📁 测试静态文件...")
    
    required_files = [
        'static/js/api-config.js',
        'static/js/main.js',
        'templates/index.html'
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = project_root / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 静态文件验证通过")
    return True

def test_api_config_js():
    """测试API配置JavaScript文件"""
    print("\n📜 测试API配置JavaScript...")
    
    try:
        api_config_path = project_root / 'static/js/api-config.js'
        content = api_config_path.read_text(encoding='utf-8')
        
        # 检查关键类和方法是否存在
        required_elements = [
            'class APIConfig',
            'generateAddress',
            'getEmails',
            'getEmailContent',
            'deleteEmail',
            'fetchWithRetry',
            'loadFromGlobalConfig'
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ API配置文件缺少元素: {', '.join(missing_elements)}")
            return False
        
        print("✅ API配置JavaScript验证通过")
        return True
        
    except Exception as e:
        print(f"❌ API配置JavaScript测试失败: {e}")
        return False

def test_template_integration():
    """测试模板集成"""
    print("\n🖼️  测试模板集成...")
    
    try:
        template_path = project_root / 'templates/index.html'
        content = template_path.read_text(encoding='utf-8')
        
        # 检查是否包含API配置相关的内容
        required_elements = [
            'api-config.js',
            'API_BASE_URL',
            'API_TIMEOUT',
            'API_RETRY_ATTEMPTS',
            'API_RETRY_DELAY'
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ 模板缺少元素: {', '.join(missing_elements)}")
            return False
        
        print("✅ 模板集成验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 模板集成测试失败: {e}")
        return False

def test_main_js_integration():
    """测试main.js集成"""
    print("\n⚙️  测试main.js集成...")
    
    try:
        main_js_path = project_root / 'static/js/main.js'
        content = main_js_path.read_text(encoding='utf-8')
        
        # 检查是否移除了硬编码的fetch调用
        hardcoded_patterns = [
            "fetch('/api/generate-address'",
            "fetch('/api/emails'",
            "fetch(`/api/email/${emailId}`",
            "fetch('/api/delete-email'"
        ]
        
        found_hardcoded = []
        for pattern in hardcoded_patterns:
            if pattern in content:
                found_hardcoded.append(pattern)
        
        # 检查是否使用了API配置
        api_config_calls = [
            'window.apiConfig.generateAddress',
            'window.apiConfig.getEmails',
            'window.apiConfig.getEmailContent',
            'window.apiConfig.deleteEmail'
        ]
        
        missing_api_calls = []
        for call in api_config_calls:
            if call not in content:
                missing_api_calls.append(call)
        
        if found_hardcoded:
            print(f"⚠️  仍然存在硬编码调用: {', '.join(found_hardcoded)}")
            print("   建议完全移除硬编码调用")
        
        if missing_api_calls:
            print(f"❌ 缺少API配置调用: {', '.join(missing_api_calls)}")
            return False
        
        if not found_hardcoded:
            print("✅ main.js集成验证通过，已移除所有硬编码调用")
        else:
            print("⚠️  main.js集成部分完成，仍有改进空间")
        
        return True
        
    except Exception as e:
        print(f"❌ main.js集成测试失败: {e}")
        return False

def test_env_example():
    """测试环境变量示例文件"""
    print("\n🌍 测试环境变量示例...")
    
    try:
        env_example_path = project_root / '.env.example'
        if not env_example_path.exists():
            print("❌ .env.example文件不存在")
            return False
        
        content = env_example_path.read_text(encoding='utf-8')
        
        # 检查新的API配置是否包含在示例中
        required_vars = [
            'API_BASE_URL',
            'API_TIMEOUT',
            'API_RETRY_ATTEMPTS',
            'API_RETRY_DELAY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if var not in content:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ .env.example缺少变量: {', '.join(missing_vars)}")
            return False
        
        print("✅ 环境变量示例验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 环境变量示例测试失败: {e}")
        return False

def test_documentation():
    """测试文档"""
    print("\n📚 测试文档...")
    
    try:
        doc_path = project_root / 'docs/API_CONFIG_GUIDE.md'
        if not doc_path.exists():
            print("❌ API配置指南文档不存在")
            return False
        
        content = doc_path.read_text(encoding='utf-8')
        
        # 检查文档是否包含关键内容
        required_sections = [
            '# API配置系统使用指南',
            '## 配置选项',
            '## 使用方法',
            '## 故障排除'
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in content:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ 文档缺少章节: {', '.join(missing_sections)}")
            return False
        
        print("✅ 文档验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 文档测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 API配置改进验证开始...\n")
    
    tests = [
        test_flask_config,
        test_static_files,
        test_api_config_js,
        test_template_integration,
        test_main_js_integration,
        test_env_example,
        test_documentation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！API配置改进成功！")
        print("\n✨ 主要改进:")
        print("   - 移除了硬编码的API端点")
        print("   - 添加了重试机制和错误处理")
        print("   - 支持环境配置")
        print("   - 提供了完整的文档")
        return True
    else:
        print(f"⚠️  有 {total - passed} 个测试失败，需要进一步检查")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
