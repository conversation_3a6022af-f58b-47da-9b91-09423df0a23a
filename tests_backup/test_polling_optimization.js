// 测试轮询优化功能
describe('轮询优化测试', () => {
    let mockConsole;
    let mockFetch;
    let clock;
    let originalConsole;

    beforeEach(() => {
        // 保存原始 console
        originalConsole = window.console;
        // 模拟 console
        mockConsole = {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn()
        };
        window.console = mockConsole;

        // 模拟 fetch API
        mockFetch = jest.fn(() => 
            Promise.resolve({
                ok: true,
                json: () => Promise.resolve({ emails: [] })
            })
        );
        global.fetch = mockFetch;

        // 模拟定时器
        clock = jest.useFakeTimers();

        // 设置初始环境
        document.visibilityState = 'visible';
        window.AUTO_REFRESH_ENABLED = true;
        window.AUTO_REFRESH_INTERVAL = 3000;
    });

    afterEach(() => {
        // 恢复原始 console
        window.console = originalConsole;
        // 清理 fetch mock
        global.fetch = undefined;
        // 恢复真实定时器
        clock.restore();
        // 重置文档可见性
        document.visibilityState = 'visible';
        // 清理注册的事件监听器
        jest.clearAllMocks();
    });

    test('页面可见性变化时正确处理轮询', async () => {
        // 触发页面隐藏
        document.visibilityState = 'hidden';
        document.dispatchEvent(new Event('visibilitychange'));
        
        // 等待一段时间
        await clock.runAllAsync();
        
        // 验证轮询被停止
        expect(mockConsole.log).toHaveBeenCalledWith(
            expect.stringContaining('页面不可见，停止轮询')
        );
        
        // 触发页面显示
        document.visibilityState = 'visible';
        document.dispatchEvent(new Event('visibilitychange'));
        
        // 验证立即检查和恢复轮询
        expect(mockConsole.log).toHaveBeenCalledWith(
            expect.stringContaining('页面变为可见，立即检查新邮件并恢复正常轮询')
        );
        expect(mockFetch).toHaveBeenCalled();
    });

    test('窗口焦点变化时正确调整轮询频率', async () => {
        // 触发窗口失焦
        window.dispatchEvent(new Event('blur'));
        
        // 验证轮询间隔被调整
        expect(mockConsole.log).toHaveBeenCalledWith(
            expect.stringContaining('切换到降低频率的轮询')
        );
        
        // 快进10秒
        await clock.tickAsync(10000);
        
        // 验证在降低频率下的轮询次数
        expect(mockFetch).toHaveBeenCalledTimes(1);
        
        // 触发窗口获得焦点
        window.dispatchEvent(new Event('focus'));
        
        // 验证恢复正常轮询间隔
        expect(mockConsole.log).toHaveBeenCalledWith(
            expect.stringContaining('恢复正常轮询间隔')
        );
        
        // 快进3秒
        await clock.tickAsync(3000);
        
        // 验证恢复正常频率后的轮询
        expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    test('邮箱过期时停止轮询', async () => {
        // 模拟邮箱过期
        const refreshBtn = document.createElement('button');
        refreshBtn.disabled = true;
        document.body.appendChild(refreshBtn);
        
        // 触发轮询
        await clock.runAllAsync();
        
        // 验证轮询被停止
        expect(mockConsole.log).toHaveBeenCalledWith(
            expect.stringContaining('邮箱已过期，停止自动刷新')
        );
        
        // 清理
        document.body.removeChild(refreshBtn);
    });
});
