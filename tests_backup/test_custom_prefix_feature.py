#!/usr/bin/env python3
"""
Unit tests for custom prefix functionality.
Tests the API endpoint, validation logic, and collision handling.
"""

import unittest
import json
import sys
import os
import tempfile
import sqlite3
from unittest.mock import patch, MagicMock

# Add parent directory to path to import app
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app, get_db_connection, generate_random_address


class TestCustomPrefixFeature(unittest.TestCase):
    """Test suite for custom prefix functionality"""

    def setUp(self):
        """Set up test environment before each test"""
        self.app = app
        self.app.config['TESTING'] = True

        # Create a temporary database file for each test
        self.db_fd, self.db_path = tempfile.mkstemp(suffix='.db')
        self.app.config['DATABASE_PATH'] = self.db_path
        self.app.config['DOMAIN_NAME'] = 'test.local'  # Set domain for tests
        self.client = self.app.test_client()

        # Clear rate limit storage for tests
        # Access the rate limit storage directly
        app_module = sys.modules['app']
        if hasattr(app_module, 'rate_limit') and hasattr(app_module.rate_limit, '_storage'):
            app_module.rate_limit._storage.clear()

        # Also try to clear any decorator storage
        generate_address_func = app_module.generate_address
        if hasattr(generate_address_func, '__wrapped__'):
            # Find the rate_limit decorator in the wrapper chain
            func = generate_address_func
            while hasattr(func, '__wrapped__'):
                if hasattr(func, 'clear'):
                    func.clear()
                    break
                if hasattr(func, '_storage'):
                    func._storage.clear()
                    break
                func = func.__wrapped__

        # Create test database tables
        self._ensure_db_tables()

    def tearDown(self):
        """Clean up after each test"""
        os.close(self.db_fd)
        os.unlink(self.db_path)

    def _ensure_db_tables(self):
        """Ensure database tables exist for testing"""
        with self.app.app_context():
            conn = get_db_connection()
            # Use the correct table name from the app
            conn.execute('''
                CREATE TABLE IF NOT EXISTS temporary_emails (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    address TEXT UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL
                )
            ''')
            conn.execute('''
                CREATE TABLE IF NOT EXISTS received_mails (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email_address_id INTEGER NOT NULL,
                    subject TEXT,
                    body_text TEXT,
                    body_html TEXT,
                    sender TEXT,
                    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (email_address_id) REFERENCES temporary_emails (id)
                )
            ''')
            conn.commit()
            conn.close()

    def test_generate_address_without_custom_prefix(self):
        """Test generating address without custom prefix (default behavior)"""
        response = self.client.post('/api/generate-address',
                                   json={},
                                   content_type='application/json')

        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIsNotNone(data['data'])
        self.assertIn('address', data['data'])
        self.assertIn('@test.local', data['data']['address'])

        # Verify address follows random pattern (12 hex chars + @develop.local)
        address_prefix = data['data']['address'].split('@')[0]
        self.assertEqual(len(address_prefix), 12)

    def test_generate_address_with_valid_custom_prefix(self):
        """Test generating address with valid custom prefix"""
        test_cases = [
            'mytest',
            'test123',
            'my-test',
            'a',  # minimum length
            'a' * 20,  # maximum length
            '123',
            'test-123-abc'
        ]

        for prefix in test_cases:
            with self.subTest(prefix=prefix):
                response = self.client.post('/api/generate-address',
                                           json={'custom_prefix': prefix},
                                           content_type='application/json')

                self.assertEqual(response.status_code, 201)
                data = json.loads(response.data)
                self.assertTrue(data['success'])
                self.assertIsNotNone(data['data'])
                self.assertIn('address', data['data'])

                address = data['data']['address']
                self.assertIn('@test.local', address)

                # Check if the address starts with the custom prefix
                address_prefix = address.split('@')[0]
                self.assertTrue(address_prefix.startswith(prefix))

    def test_generate_address_with_invalid_custom_prefix(self):
        """Test generating address with invalid custom prefix"""
        test_cases = [
            'test@invalid',      # contains @
            'test.com',          # contains .
            'test space',        # contains space
            'test#hash',         # contains #
            '',                  # empty string
            'a' * 21,           # too long
            'test_underscore',   # contains underscore
            'test/slash',        # contains slash
            'test+plus',         # contains plus
        ]

        for prefix in test_cases:
            with self.subTest(prefix=prefix):
                response = self.client.post('/api/generate-address',
                                           json={'custom_prefix': prefix},
                                           content_type='application/json')

                self.assertEqual(response.status_code, 400)
                data = json.loads(response.data)
                self.assertFalse(data['success'])
                self.assertIsNotNone(data['error'])
                self.assertIn('自定义前缀只能包含字母、数字和连字符', data['error'])

    def test_custom_prefix_collision_handling(self):
        """Test collision handling when custom prefix already exists"""
        prefix = 'collision'

        # First request should succeed with exact prefix
        response1 = self.client.post('/api/generate-address',
                                    json={'custom_prefix': prefix},
                                    content_type='application/json')

        self.assertEqual(response1.status_code, 201)
        data1 = json.loads(response1.data)
        self.assertTrue(data1['success'])
        address1 = data1['data']['address']

        # Second request should succeed with modified prefix (collision handling)
        response2 = self.client.post('/api/generate-address',
                                    json={'custom_prefix': prefix},
                                    content_type='application/json')

        self.assertEqual(response2.status_code, 201)
        data2 = json.loads(response2.data)
        self.assertTrue(data2['success'])
        address2 = data2['data']['address']

        # Addresses should be different
        self.assertNotEqual(address1, address2)

        # Both should start with the prefix
        self.assertTrue(address1.split('@')[0].startswith(prefix))
        self.assertTrue(address2.split('@')[0].startswith(prefix))

    def test_api_request_validation(self):
        """Test API request validation for different input types"""
        test_cases = [
            # Valid JSON with null prefix
            ({'custom_prefix': None}, 201),
            # Valid JSON with missing prefix
            ({}, 201),
            # Invalid JSON
            ('invalid json', 400),
            # Wrong content type
            ('{}', 400),
        ]

        for request_data, expected_status in test_cases:
            with self.subTest(request_data=request_data):
                if isinstance(request_data, str):
                    # Test with plain string (should fail)
                    response = self.client.post('/api/generate-address',
                                               data=request_data,
                                               content_type='text/plain')
                else:
                    # Test with JSON
                    response = self.client.post('/api/generate-address',
                                               json=request_data,
                                               content_type='application/json')

                self.assertEqual(response.status_code, expected_status)

    def test_custom_prefix_regex_validation(self):
        """Test regex validation logic directly"""
        import re

        # This is the regex pattern used in the app
        pattern = r'^[a-zA-Z0-9\-]{1,20}$'

        valid_prefixes = [
            'test',
            'test123',
            'test-123',
            'a',
            'A' * 20,
            '123',
            'test-test-test'
        ]

        invalid_prefixes = [
            'test@test',
            'test.test',
            'test_test',
            'test test',
            '',
            'a' * 21,
            'test#test',
            'test+test'
        ]

        for prefix in valid_prefixes:
            with self.subTest(prefix=prefix, valid=True):
                self.assertTrue(re.match(pattern, prefix),
                               f"'{prefix}' should be valid")

        for prefix in invalid_prefixes:
            with self.subTest(prefix=prefix, valid=False):
                self.assertFalse(re.match(pattern, prefix),
                                f"'{prefix}' should be invalid")

    def test_generate_random_address_function(self):
        """Test the generate_random_address function directly"""
        with self.app.app_context():
            # Test without custom prefix
            address1 = generate_random_address()
            self.assertIsInstance(address1, str)
            self.assertIn('@test.local', address1)
            self.assertEqual(len(address1.split('@')[0]), 12)

            # Test with custom prefix
            address2 = generate_random_address('myprefix')
            self.assertIsInstance(address2, str)
            self.assertIn('@test.local', address2)
            self.assertTrue(address2.startswith('myprefix'))

            # Ensure randomness - multiple calls should produce different addresses
            addresses = [generate_random_address() for _ in range(5)]
            self.assertEqual(len(set(addresses)), 5, "All generated addresses should be unique")


class TestCustomPrefixIntegration(unittest.TestCase):
    """Integration tests for custom prefix functionality"""

    def setUp(self):
        """Set up test environment"""
        self.app = app
        self.app.config['TESTING'] = True

        # Create a temporary database file for each test
        self.db_fd, self.db_path = tempfile.mkstemp(suffix='.db')
        self.app.config['DATABASE_PATH'] = self.db_path
        self.app.config['DOMAIN_NAME'] = 'test.local'  # Set domain for tests
        self.client = self.app.test_client()

        # Clear rate limit storage for tests
        # We need to access the actual rate_limit decorator used on generate_address
        from app import generate_address
        if hasattr(generate_address, '__wrapped__'):
            # Find the rate_limit decorator in the wrapper chain
            func = generate_address
            while hasattr(func, '__wrapped__'):
                if hasattr(func, 'clear'):
                    func.clear()
                    break
                func = func.__wrapped__

        # Create test database tables
        self._ensure_db_tables()

    def tearDown(self):
        """Clean up after each test"""
        os.close(self.db_fd)
        os.unlink(self.db_path)

    def _ensure_db_tables(self):
        """Ensure database tables exist for testing"""
        with self.app.app_context():
            conn = get_db_connection()
            # Use the correct table name from the app
            conn.execute('''
                CREATE TABLE IF NOT EXISTS temporary_emails (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    address TEXT UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL
                )
            ''')
            conn.execute('''
                CREATE TABLE IF NOT EXISTS received_mails (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email_address_id INTEGER NOT NULL,
                    subject TEXT,
                    body_text TEXT,
                    body_html TEXT,
                    sender TEXT,
                    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (email_address_id) REFERENCES temporary_emails (id)
                )
            ''')
            conn.commit()
            conn.close()

    def test_end_to_end_custom_prefix_workflow(self):
        """Test complete workflow: generate -> verify -> list"""
        prefix = 'e2etest'

        # Step 1: Generate address with custom prefix
        response = self.client.post('/api/generate-address',
                                   json={'custom_prefix': prefix},
                                   content_type='application/json')

        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertTrue(data['success'])

        generated_address = data['data']['address']
        self.assertTrue(generated_address.startswith(prefix))

        # Step 2: List emails to verify address was stored
        response = self.client.get(f'/api/emails?address={generated_address}')
        self.assertEqual(response.status_code, 200)

        data = json.loads(response.data)
        self.assertTrue(data['success'])

        # The response should contain an empty emails list since no emails were sent
        self.assertIn('emails', data['data'])
        self.assertIsInstance(data['data']['emails'], list)


if __name__ == '__main__':
    # Run tests with verbose output
    unittest.main(verbosity=2, argv=[''])
