import requests
import sqlite3
import os
import time
import sys
import logging
from pathlib import Path
from datetime import datetime, timezone
from dotenv import load_dotenv, find_dotenv

# 尝试加载环境变量
dotenv_path = find_dotenv()
if dotenv_path:
    load_dotenv(dotenv_path)
    print(f"已加载环境变量: {dotenv_path}")

# 从环境变量获取API设置
API_HOST = os.getenv('API_HOST', 'http://127.0.0.1')
API_PORT = os.getenv('API_PORT', '5000')
# 修复API_BASE构建逻辑：检查是否已经包含端口号
if '://' in API_HOST and ':' in API_HOST.split('://', 1)[1]:
    # 如果API_HOST已经包含端口号（如 http://127.0.0.1:5000）
    API_BASE = f"{API_HOST.rstrip('/')}/api"
else:
    # 如果API_HOST不包含端口号，需要添加端口号
    API_BASE = f"{API_HOST.rstrip('/')}:{API_PORT}/api"
SKIP_API_CHECK = os.getenv('SKIP_API_CHECK', 'false').lower() == 'true'

# 获取数据库路径
project_root = Path(__file__).resolve().parent.parent
DB_PATH = os.getenv('DATABASE_PATH', str(project_root / 'database/tempmail.db'))


def get_current_web_email():
    """从数据库中获取最近创建的邮箱地址

    这模拟获取Web界面当前使用的邮箱的过程，通过获取数据库中最新创建的邮箱
    """
    try:
        db_conn = sqlite3.connect(DB_PATH)
        db_cursor = db_conn.cursor()

        # 获取未过期的最新邮箱
        now_utc = datetime.now(timezone.utc).isoformat()
        db_cursor.execute(
            "SELECT id, address FROM temporary_emails WHERE expires_at > ? ORDER BY created_at DESC LIMIT 1",
            (now_utc,)
        )

        row = db_cursor.fetchone()
        db_conn.close()

        if not row:
            # 如果没有找到有效的邮箱，则通过API生成一个新的
            print("数据库中没有找到有效的邮箱，将通过API生成一个新的")
            return generate_new_email()

        email_id, email_address = row
        print(f"从数据库获取到当前使用的邮箱: {email_address} (ID: {email_id})")
        return email_address, email_id
    except sqlite3.Error as e:
        print(f"数据库错误: {e}")
        return generate_new_email()
    except (ValueError, TypeError) as e:
        print(f"数据解析错误: {e}")
        return generate_new_email()


def generate_new_email():
    """通过API生成一个新的邮箱地址（仅当无法获取当前邮箱时使用）"""
    try:
        resp = requests.post(f"{API_BASE}/generate-address", timeout=5)
        if not resp.ok:
            raise RuntimeError(f"API请求失败: {resp.status_code} {resp.reason}")

        data = resp.json()
        if not data.get("success"):
            raise RuntimeError(f"邮箱生成失败: {data.get('error')}")

        email_address = data["data"]["address"]

        # 获取这个新生成邮箱的ID
        db_conn = sqlite3.connect(DB_PATH)
        db_cursor = db_conn.cursor()
        db_cursor.execute("SELECT id FROM temporary_emails WHERE address = ?", (email_address,))
        row = db_cursor.fetchone()
        db_conn.close()

        if not row:
            raise RuntimeError(f"新生成的邮箱 {email_address} 未在数据库中找到")

        email_id = row[0]
        print(f"通过API生成了新邮箱: {email_address} (ID: {email_id})")
        return email_address, email_id
    except requests.RequestException as e:
        print(f"API请求错误: {e}")
        raise
    except sqlite3.Error as e:
        print(f"数据库错误: {e}")
        raise
    except (ValueError, TypeError, KeyError) as e:
        print(f"数据解析错误: {e}")
        raise


def send_mail(email_id, mail_subject, mail_body):
    """插入一封测试邮件，使用UTC时间戳"""
    now = datetime.now(timezone.utc)
    db_conn = sqlite3.connect(DB_PATH)
    db_cursor = db_conn.cursor()
    db_cursor.execute(
        "INSERT INTO received_mails (email_address_id, sender, subject, body_text, received_at) VALUES (?, ?, ?, ?, ?)",
        (email_id, '<EMAIL>', mail_subject, mail_body, now.isoformat())
    )
    db_conn.commit()
    db_conn.close()


def test_polling():
    """测试邮件轮询功能，使用Web当前使用的邮箱地址"""
    # 1. 获取Web当前使用的邮箱地址
    test_email_address, test_email_id = get_current_web_email()
    print(f"使用邮箱: {test_email_address} (ID: {test_email_id})")

    # 2. 插入测试邮件
    for i in range(5):
        subject = f"测试邮件 #{i+1}"
        body = f"这是一封测试邮件，编号 {i+1}，发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        send_mail(test_email_id, subject, body)
        if i < 4:
            time.sleep(1)

    # 3. 验证邮件已成功插入数据库
    db_conn = sqlite3.connect(DB_PATH)
    db_cursor = db_conn.cursor()
    db_cursor.execute(
        "SELECT COUNT(*) FROM received_mails WHERE email_address_id = ? AND subject LIKE '测试邮件 #%'",
        (test_email_id,)
    )
    email_count_db = db_cursor.fetchone()[0]
    db_conn.close()
    assert email_count_db >= 5, f"数据库中的邮件数量不足: 期望至少5封，实际{email_count_db}封"
    print(f"数据库验证通过，邮箱 ID {test_email_id} 收件箱邮件数：{email_count_db}")

    # 4. 如果不跳过API检查，则通过API获取收件箱
    if not SKIP_API_CHECK:
        try:
            print(f"尝试连接API: {API_BASE}")
            resp = requests.get(f"{API_BASE}/emails", params={"address": test_email_address}, timeout=5)
            data = resp.json()
            assert data["success"], f"收件箱获取失败: {data.get('error')}"
            emails = data["data"]["emails"]
            assert len(emails) >= 5, f"邮件数量不足: 期望至少5封，实际{len(emails)}封"
            print(f"API集成测试通过，邮箱 {test_email_address} 收件箱邮件数：{len(emails)}")
        except (requests.RequestException, AssertionError) as e:
            if SKIP_API_CHECK:
                print(f"警告: API连接失败，但已设置SKIP_API_CHECK=true，继续测试: {e}")
            else:
                print(f"API连接失败: {e}")
                if os.getenv('CI') != 'true':  # 非CI环境下可以跳过API测试
                    print("在非CI环境中，API测试失败不会导致整个测试失败")
                    print("如需完全跳过API测试，请设置环境变量 SKIP_API_CHECK=true")
                else:
                    raise  # 在CI环境中重新抛出异常
    else:
        print("已跳过API测试 (SKIP_API_CHECK=true)")

    # 5. 提示用户
    print("\n========= 测试信息 =========")
    print("测试邮件已发送到Web界面当前使用的邮箱:")
    print(f"邮箱地址: {test_email_address}")
    print("==============================")
    # 不返回任何值，避免pytest警告


if __name__ == "__main__":
    test_polling()