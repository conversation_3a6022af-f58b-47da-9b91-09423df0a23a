# 清理脚本测试说明

## 概述

`test_cleanup_script.py` 包含了对 `cleanup_script.py` 的完整测试套件，测试覆盖了清理脚本的各种场景和边界条件。

## 测试覆盖率

✅ **100% 代码覆盖率** - 所有代码行都被测试覆盖

## 测试内容

### 主要测试类

#### `TestCleanupExpiredEmails`
测试清理过期邮箱的核心功能：

- **基本功能测试**：
  - `test_cleanup_no_expired_emails` - 测试无过期邮箱时的处理
  - `test_cleanup_expired_emails_without_mails` - 测试清理过期邮箱（无邮件）
  - `test_cleanup_expired_emails_with_mails` - 测试清理过期邮箱（包含邮件）

- **参数和配置测试**：
  - `test_cleanup_with_database_path_parameter` - 测试通过参数传递数据库路径
  - `test_cleanup_no_database_path` - 测试无数据库路径配置的处理
  - `test_cleanup_missing_database_path_env` - 测试环境变量缺失的处理

- **错误处理测试**：
  - `test_cleanup_database_error` - 测试数据库连接错误
  - `test_cleanup_general_exception` - 测试一般异常处理
  - `test_cleanup_transaction_rollback` - 测试事务回滚

- **边界条件测试**：
  - `test_cleanup_boundary_time_conditions` - 测试时间边界条件
  - `test_cleanup_large_dataset` - 测试大量数据清理
  - `test_cleanup_utf8_email_addresses` - 测试UTF-8字符邮箱地址
  - `test_cleanup_creates_database_directory` - 测试数据库目录创建

- **系统功能测试**：
  - `test_cleanup_creates_database_directory` - 测试自动创建数据库目录

#### `TestCleanupScriptMain`
测试脚本主执行逻辑：

- `test_main_execution` - 测试主函数执行
- `test_script_can_be_imported` - 测试脚本可正常导入

#### `TestCleanupScriptLogging`
测试日志配置：

- `test_logging_configuration` - 测试日志配置
- `test_custom_log_file` - 测试自定义日志文件
- `test_no_log_file_configuration` - 测试无日志文件配置
- `test_log_directory_creation` - 测试日志目录创建
- `test_main_script_execution` - 测试脚本主执行块

## 运行测试

### 运行所有清理脚本测试
```bash
python -m pytest tests/test_cleanup_script.py -v
```

### 运行特定测试类
```bash
python -m pytest tests/test_cleanup_script.py::TestCleanupExpiredEmails -v
```

### 运行特定测试方法
```bash
python -m pytest tests/test_cleanup_script.py::TestCleanupExpiredEmails::test_cleanup_expired_emails_with_mails -v
```

### 运行测试并显示覆盖率
```bash
python -m pytest tests/test_cleanup_script.py --cov=cleanup_script --cov-report=html
```

## 测试特性

### 自动测试数据库设置
- 每个测试使用独立的临时数据库
- 自动创建测试表结构
- 启用外键约束以测试级联删除
- 测试结束后自动清理

### Mock和模拟
- 使用 `unittest.mock` 模拟数据库错误
- 模拟环境变量配置
- 模拟日志记录

### 日志测试
- 使用 `caplog` 捕获和验证日志输出
- 测试不同日志级别的输出

## 测试数据管理

### 辅助方法
- `_init_test_db()` - 初始化测试数据库结构
- `_insert_test_email()` - 插入测试邮箱数据
- `_count_records()` - 统计数据库记录数

### 测试数据特点
- 使用UTC时间避免时区问题
- 支持过期和未过期邮箱
- 支持带邮件和不带邮件的邮箱
- 自动清理测试数据

## 重要注意事项

1. **外键约束**：测试确保SQLite外键约束被正确启用，以测试级联删除功能
2. **时间处理**：所有时间相关测试使用UTC时间避免时区问题
3. **异常处理**：全面测试各种异常情况和错误恢复
4. **资源清理**：每个测试后自动清理临时数据库文件

## 测试覆盖率

这些测试覆盖了 `cleanup_script.py` 的以下方面：
- ✅ 核心清理功能
- ✅ 数据库操作
- ✅ 错误处理
- ✅ 日志记录
- ✅ 配置管理
- ✅ 边界条件
- ✅ 异常情况
- ✅ 级联删除（外键约束）
- ✅ 主执行块
- ✅ 日志配置分支

## 测试结果

```
============================================================== tests coverage ==============================================================
Name                Stmts   Miss  Cover   Missing
-------------------------------------------------
cleanup_script.py      54      0   100%
-------------------------------------------------
TOTAL                  54      0   100%
============================================================ 21 passed in 0.32s ============================================================
```

所有21个测试用例全部通过，代码覆盖率达到100%。
