# tests/test_cleanup_script.py
import os
import sys
import sqlite3
import tempfile
import logging
import shutil
import importlib
import subprocess
from datetime import datetime, timedelta, timezone
from unittest.mock import patch, MagicMock, call
from pathlib import Path

import pytest

# Add the project root directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import cleanup_script
from cleanup_script import cleanup_expired_emails


class TestCleanupExpiredEmails:
    """测试清理过期邮箱功能"""

    def setup_method(self):
        """每个测试方法的设置"""
        # 创建临时数据库文件
        self.db_fd, self.db_path = tempfile.mkstemp(suffix='.db')
        self.temp_db_path = Path(self.db_path)

        # 初始化测试数据库
        self._init_test_db()

    def teardown_method(self):
        """每个测试方法的清理"""
        os.close(self.db_fd)
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)

    def _init_test_db(self):
        """初始化测试数据库结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建测试表结构
        cursor.execute("""
            CREATE TABLE temporary_emails (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                address TEXT UNIQUE NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
        """)

        cursor.execute("""
            CREATE TABLE received_mails (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email_address_id INTEGER NOT NULL,
                sender TEXT,
                subject TEXT,
                body_text TEXT,
                body_html TEXT,
                received_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (email_address_id) REFERENCES temporary_emails (id) ON DELETE CASCADE
            )
        """)

        # 启用外键约束（SQLite默认关闭）
        cursor.execute("PRAGMA foreign_keys = ON")

        conn.commit()
        conn.close()

    def _insert_test_email(self, address, hours_offset=0):
        """插入测试邮箱数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("PRAGMA foreign_keys = ON")

        now_utc = datetime.now(timezone.utc)
        expires_at = now_utc + timedelta(hours=hours_offset)

        cursor.execute(
            "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
            (address, expires_at.isoformat(), now_utc.isoformat())
        )

        email_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return email_id

    def _insert_test_mail(self, email_id, sender="<EMAIL>", subject="Test Subject"):
        """插入测试邮件数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("PRAGMA foreign_keys = ON")

        cursor.execute(
            """INSERT INTO received_mails
               (email_address_id, sender, subject, body_text, body_html)
               VALUES (?, ?, ?, ?, ?)""",
            (email_id, sender, subject, "Test body text", "<p>Test body html</p>")
        )

        conn.commit()
        conn.close()

    def _count_records(self):
        """统计数据库中的记录数"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("PRAGMA foreign_keys = ON")

        cursor.execute("SELECT COUNT(*) FROM temporary_emails")
        email_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM received_mails")
        mail_count = cursor.fetchone()[0]

        conn.close()
        return email_count, mail_count

    def test_cleanup_no_expired_emails(self, caplog):
        """测试没有过期邮箱时的清理"""
        # 插入一些未过期的邮箱
        self._insert_test_email("<EMAIL>", hours_offset=1)
        self._insert_test_email("<EMAIL>", hours_offset=2)

        with caplog.at_level(logging.INFO):
            cleanup_expired_emails(self.db_path)

        # 验证没有邮箱被删除
        email_count, mail_count = self._count_records()
        assert email_count == 2
        assert mail_count == 0

        # 验证日志信息
        assert "没有找到需要清理的过期邮箱地址。" in caplog.text

    def test_cleanup_expired_emails_without_mails(self, caplog):
        """测试清理过期邮箱（无邮件）"""
        # 插入过期邮箱
        self._insert_test_email("<EMAIL>", hours_offset=-1)
        self._insert_test_email("<EMAIL>", hours_offset=-2)
        # 插入未过期邮箱
        self._insert_test_email("<EMAIL>", hours_offset=1)

        with caplog.at_level(logging.INFO):
            cleanup_expired_emails(self.db_path)

        # 验证只删除了过期的邮箱
        email_count, mail_count = self._count_records()
        assert email_count == 1
        assert mail_count == 0

        # 验证日志信息
        assert "准备删除过期邮箱: <EMAIL>" in caplog.text
        assert "准备删除过期邮箱: <EMAIL>" in caplog.text
        assert "清理完成。共删除了 2 个过期邮箱地址。" in caplog.text

    def test_cleanup_expired_emails_with_mails(self, caplog):
        """测试清理过期邮箱（包含邮件）"""
        # 插入过期邮箱和邮件
        expired_id = self._insert_test_email("<EMAIL>", hours_offset=-1)
        self._insert_test_mail(expired_id, "<EMAIL>", "Subject 1")
        self._insert_test_mail(expired_id, "<EMAIL>", "Subject 2")

        # 插入未过期邮箱和邮件
        valid_id = self._insert_test_email("<EMAIL>", hours_offset=1)
        self._insert_test_mail(valid_id, "<EMAIL>", "Subject 3")
        self._insert_test_mail(valid_id, "<EMAIL>", "Subject 4")

        with caplog.at_level(logging.INFO):
            cleanup_expired_emails(self.db_path)

        # 验证过期邮箱及其邮件被删除
        email_count, mail_count = self._count_records()
        assert email_count == 1  # 只剩下未过期的邮箱
        assert mail_count == 2   # 只剩下未过期邮箱的邮件

        # 验证删除的是正确的邮箱
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT address FROM temporary_emails")
        remaining_address = cursor.fetchone()[0]
        assert remaining_address == "<EMAIL>"
        conn.close()

        # 验证日志信息
        assert "准备删除过期邮箱: <EMAIL>" in caplog.text
        assert "清理完成。共删除了 1 个过期邮箱地址。" in caplog.text

    def test_cleanup_with_database_path_parameter(self, caplog):
        """测试通过参数传递数据库路径"""
        self._insert_test_email("<EMAIL>", hours_offset=-1)

        # 直接传递数据库路径参数
        with caplog.at_level(logging.INFO):
            cleanup_expired_emails(database_path=self.db_path)

        email_count, mail_count = self._count_records()
        assert email_count == 0
        assert "清理完成。共删除了 1 个过期邮箱地址。" in caplog.text

    @patch.dict(os.environ, {'DATABASE_PATH': ''}, clear=False)
    def test_cleanup_no_database_path(self, caplog):
        """测试没有配置数据库路径的情况"""
        with caplog.at_level(logging.ERROR):
            cleanup_expired_emails()

        assert "DATABASE_PATH 未配置。清理中止。" in caplog.text

    @patch.dict(os.environ, {}, clear=True)
    def test_cleanup_missing_database_path_env(self, caplog):
        """测试环境变量中缺少DATABASE_PATH的情况"""
        with caplog.at_level(logging.ERROR):
            cleanup_expired_emails()

        assert "DATABASE_PATH 未配置。清理中止。" in caplog.text

    def test_cleanup_creates_database_directory(self, caplog):
        """测试清理脚本会创建数据库目录"""
        # 创建一个不存在目录中的数据库路径
        temp_dir = tempfile.mkdtemp()
        new_db_path = os.path.join(temp_dir, "subdir", "test.db")

        try:
            # 确保子目录不存在
            assert not os.path.exists(os.path.dirname(new_db_path))

            # 尝试清理（会创建目录和数据库）
            with caplog.at_level(logging.INFO):
                cleanup_expired_emails(database_path=new_db_path)

            # 验证目录被创建
            assert os.path.exists(os.path.dirname(new_db_path))

        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)

    @patch('cleanup_script.sqlite3.connect')
    def test_cleanup_database_error(self, mock_connect, caplog):
        """测试数据库连接错误的处理"""
        mock_connect.side_effect = sqlite3.Error("数据库连接失败")

        with caplog.at_level(logging.ERROR):
            cleanup_expired_emails(database_path=self.db_path)

        assert "清理过程中发生数据库错误: 数据库连接失败" in caplog.text

    @patch('cleanup_script.sqlite3.connect')
    def test_cleanup_general_exception(self, mock_connect, caplog):
        """测试一般异常的处理"""
        mock_connect.side_effect = Exception("未知错误")

        with caplog.at_level(logging.ERROR):
            cleanup_expired_emails(database_path=self.db_path)

        assert "清理过程中发生未捕获的异常: 未知错误" in caplog.text

    @patch('cleanup_script.sqlite3.connect')
    def test_cleanup_transaction_rollback(self, mock_connect, caplog):
        """测试数据库错误时的事务回滚"""
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_connect.return_value = mock_conn
        mock_conn.cursor.return_value = mock_cursor

        # 模拟在删除操作时发生错误
        mock_cursor.execute.side_effect = [
            None,  # 第一次查询（获取过期邮箱）成功
            sqlite3.Error("删除操作失败")  # 第二次执行（删除操作）失败
        ]
        mock_cursor.fetchall.return_value = [('1', '<EMAIL>')]

        with caplog.at_level(logging.ERROR):
            cleanup_expired_emails(database_path=self.db_path)

        # 验证回滚被调用
        mock_conn.rollback.assert_called_once()
        assert "清理过程中发生数据库错误" in caplog.text

    def test_cleanup_boundary_time_conditions(self, caplog):
        """测试边界时间条件"""
        now_utc = datetime.now(timezone.utc)

        # 插入刚好过期的邮箱（过期1秒）
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("PRAGMA foreign_keys = ON")

        just_expired_time = (now_utc - timedelta(seconds=1)).isoformat()
        just_valid_time = (now_utc + timedelta(seconds=1)).isoformat()

        cursor.execute(
            "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
            ("<EMAIL>", just_expired_time, now_utc.isoformat())
        )

        cursor.execute(
            "INSERT INTO temporary_emails (address, expires_at, created_at) VALUES (?, ?, ?)",
            ("<EMAIL>", just_valid_time, now_utc.isoformat())
        )

        conn.commit()
        conn.close()

        with caplog.at_level(logging.INFO):
            cleanup_expired_emails(self.db_path)

        # 验证只删除了过期的邮箱
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("PRAGMA foreign_keys = ON")
        cursor.execute("SELECT address FROM temporary_emails")
        remaining_addresses = [row[0] for row in cursor.fetchall()]
        conn.close()

        assert "<EMAIL>" in remaining_addresses
        assert "<EMAIL>" not in remaining_addresses
        assert "清理完成。共删除了 1 个过期邮箱地址。" in caplog.text

    def test_cleanup_large_dataset(self, caplog):
        """测试大量数据的清理"""
        # 插入100个过期邮箱和50个有效邮箱
        for i in range(100):
            self._insert_test_email(f"expired{i}@test.com", hours_offset=-1)

        for i in range(50):
            self._insert_test_email(f"valid{i}@test.com", hours_offset=1)

        with caplog.at_level(logging.INFO):
            cleanup_expired_emails(self.db_path)

        email_count, _ = self._count_records()
        assert email_count == 50  # 只剩下有效邮箱
        assert "清理完成。共删除了 100 个过期邮箱地址。" in caplog.text

    def test_cleanup_utf8_email_addresses(self, caplog):
        """测试包含UTF-8字符的邮箱地址清理"""
        # 插入包含特殊字符的邮箱地址
        special_addresses = [
            "测试@test.com",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]

        for addr in special_addresses:
            self._insert_test_email(addr, hours_offset=-1)

        with caplog.at_level(logging.INFO):
            cleanup_expired_emails(self.db_path)

        email_count, _ = self._count_records()
        assert email_count == 0

        # 验证所有特殊字符邮箱都被正确记录和删除
        for addr in special_addresses:
            assert f"准备删除过期邮箱: {addr}" in caplog.text

    @patch.dict(os.environ, {'DATABASE_PATH': '/non/existent/path/test.db'}, clear=False)
    def test_cleanup_with_env_database_path(self, caplog):
        """测试使用环境变量中的数据库路径"""
        with caplog.at_level(logging.INFO):
            cleanup_expired_emails()

        # 由于路径不存在，应该会尝试创建目录但可能失败
        # 这里主要测试环境变量是否被正确读取


class TestCleanupScriptMain:
    """测试清理脚本的主执行逻辑"""

    @patch('cleanup_script.cleanup_expired_emails')
    @patch('cleanup_script.logging')
    def test_main_execution(self, mock_logging, mock_cleanup):
        """测试主函数的执行"""
        # 直接测试主逻辑块
        mock_logging.info = MagicMock()

        # 模拟执行主逻辑
        mock_logging.info("清理脚本开始执行。")
        mock_cleanup()
        mock_logging.info("清理脚本执行完毕。")

        # 验证调用
        mock_cleanup.assert_called_once()
        mock_logging.info.assert_has_calls([
            call("清理脚本开始执行。"),
            call("清理脚本执行完毕。")
        ])

    def test_script_can_be_imported(self):
        """测试脚本可以被正常导入"""
        # 验证关键函数存在
        assert hasattr(cleanup_script, 'cleanup_expired_emails')
        assert callable(cleanup_script.cleanup_expired_emails)


class TestCleanupScriptLogging:
    """测试清理脚本的日志配置"""

    def test_logging_configuration(self):
        """测试日志配置是否正确"""
        # 验证模块级别的配置存在
        assert hasattr(cleanup_script, 'DATABASE_PATH')
        assert hasattr(cleanup_script, 'LOG_FILE_CLEANUP')

        # 验证清理函数存在
        assert callable(cleanup_script.cleanup_expired_emails)

    @patch.dict(os.environ, {'LOG_FILE_CLEANUP': '/tmp/test_cleanup.log'}, clear=False)
    def test_custom_log_file(self):
        """测试自定义日志文件配置"""
        # 重新加载模块以应用新的环境变量
        importlib.reload(cleanup_script)

        # 验证日志文件路径被正确设置
        assert cleanup_script.LOG_FILE_CLEANUP == '/tmp/test_cleanup.log'

    @patch.dict(os.environ, {'LOG_FILE_CLEANUP': ''}, clear=False)
    def test_no_log_file_configuration(self):
        """测试没有日志文件时的配置"""
        # 重新加载模块以应用新的环境变量
        importlib.reload(cleanup_script)

        # 验证日志文件路径为空
        assert cleanup_script.LOG_FILE_CLEANUP == ''

    @patch.dict(os.environ, {'LOG_FILE_CLEANUP': '/tmp/nonexistent/dir/test.log'}, clear=False)
    def test_log_directory_creation(self):
        """测试日志目录创建"""
        # 重新加载模块以应用新的环境变量
        importlib.reload(cleanup_script)

        # 验证日志文件路径被正确设置
        assert cleanup_script.LOG_FILE_CLEANUP == '/tmp/nonexistent/dir/test.log'


    def test_main_script_execution(self):
        """测试脚本作为主程序执行"""
        # 使用subprocess运行脚本来测试主执行块
        # 创建一个临时数据库用于测试
        temp_dir = tempfile.mkdtemp()
        test_db_path = os.path.join(temp_dir, "test.db")

        try:
            # 设置环境变量
            env = os.environ.copy()
            env['DATABASE_PATH'] = test_db_path
            env['LOG_FILE_CLEANUP'] = ''  # 使用stderr输出

            # 运行cleanup_script.py作为主程序
            result = subprocess.run(
                [sys.executable, 'cleanup_script.py'],
                cwd=os.path.dirname(os.path.abspath(__file__ + '/..')),
                env=env,
                capture_output=True,
                text=True,
                timeout=10,
                check=False
            )

            # 验证脚本成功执行
            assert result.returncode == 0
            # 验证日志输出包含预期信息
            assert "清理脚本开始执行" in result.stderr or "清理脚本开始执行" in result.stdout

        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)


if __name__ == "__main__":
    pytest.main([__file__])