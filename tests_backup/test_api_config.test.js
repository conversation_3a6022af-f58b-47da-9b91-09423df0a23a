/**
 * API配置模块测试
 * 验证API配置功能的正确性
 */

// 模拟测试环境
const mockWindow = {
    appConfig: {
        API_BASE_URL: 'https://test-api.tempmail.com',
        API_TIMEOUT: 8000,
        API_RETRY_ATTEMPTS: 2,
        API_RETRY_DELAY: 500
    }
};

// 模拟fetch函数
const mockFetch = jest.fn();
global.fetch = mockFetch;

// 导入APIConfig类（需要适配Node.js环境）
const APIConfig = require('../static/js/api-config.js');

describe('APIConfig', () => {
    let apiConfig;

    beforeEach(() => {
        // 重置mock
        mockFetch.mockReset();
        
        // 创建新的APIConfig实例
        global.window = mockWindow;
        apiConfig = new APIConfig();
    });

    describe('配置加载', () => {
        test('应该从全局配置正确加载API设置', () => {
            const config = apiConfig.getConfig();
            
            expect(config.baseUrl).toBe('https://test-api.tempmail.com');
            expect(config.timeout).toBe(8000);
            expect(config.retryAttempts).toBe(2);
            expect(config.retryDelay).toBe(500);
        });

        test('应该使用默认配置当没有全局配置时', () => {
            global.window = { appConfig: {} };
            const apiConfigDefault = new APIConfig();
            const config = apiConfigDefault.getConfig();
            
            expect(config.baseUrl).toBe('');
            expect(config.timeout).toBe(10000);
            expect(config.retryAttempts).toBe(3);
            expect(config.retryDelay).toBe(1000);
        });
    });

    describe('URL生成', () => {
        test('应该正确生成API URL', () => {
            const url = apiConfig.getUrl('generateAddress');
            expect(url).toBe('https://test-api.tempmail.com/api/generate-address');
        });

        test('应该正确处理查询参数', () => {
            const url = apiConfig.getUrl('getEmails', { 
                address: '<EMAIL>',
                last_received: '2023-01-01T00:00:00Z'
            });
            expect(url).toContain('address=test%40example.com');
            expect(url).toContain('last_received=2023-01-01T00%3A00%3A00Z');
        });

        test('应该忽略null和undefined参数', () => {
            const url = apiConfig.getUrl('getEmails', { 
                address: '<EMAIL>',
                last_received: null,
                undefined_param: undefined
            });
            expect(url).toBe('https://test-api.tempmail.com/api/emails?address=test%40example.com');
        });
    });

    describe('请求选项', () => {
        test('应该返回正确的默认请求选项', () => {
            const options = apiConfig.getRequestOptions();
            
            expect(options.headers['Content-Type']).toBe('application/json');
            expect(options.headers['Accept']).toBe('application/json');
            expect(options.timeout).toBe(8000);
        });

        test('应该正确合并自定义选项', () => {
            const options = apiConfig.getRequestOptions({
                method: 'POST',
                headers: { 'X-Custom': 'test' }
            });
            
            expect(options.method).toBe('POST');
            expect(options.headers['X-Custom']).toBe('test');
            expect(options.headers['Content-Type']).toBe('application/json');
        });
    });

    describe('重试机制', () => {
        test('应该在网络错误时重试', async () => {
            // 模拟网络错误
            mockFetch
                .mockRejectedValueOnce(new Error('Network error'))
                .mockRejectedValueOnce(new Error('Network error'))
                .mockResolvedValueOnce({
                    ok: true,
                    json: async () => ({ success: true, data: 'success' })
                });

            const response = await apiConfig.fetchWithRetry('https://test.com');
            
            expect(mockFetch).toHaveBeenCalledTimes(3);
            expect(response.ok).toBe(true);
        });

        test('应该在5xx错误时重试', async () => {
            // 模拟5xx错误
            mockFetch
                .mockResolvedValueOnce({
                    ok: false,
                    status: 500,
                    statusText: 'Internal Server Error'
                })
                .mockResolvedValueOnce({
                    ok: true,
                    json: async () => ({ success: true, data: 'success' })
                });

            const response = await apiConfig.fetchWithRetry('https://test.com');
            
            expect(mockFetch).toHaveBeenCalledTimes(2);
            expect(response.ok).toBe(true);
        });

        test('应该在达到最大重试次数后抛出错误', async () => {
            // 模拟持续失败
            mockFetch.mockRejectedValue(new Error('Persistent error'));

            await expect(apiConfig.fetchWithRetry('https://test.com'))
                .rejects.toThrow('API请求失败，已重试2次: Persistent error');
            
            expect(mockFetch).toHaveBeenCalledTimes(2);
        });

        test('应该在4xx错误时不重试', async () => {
            // 模拟4xx错误
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: 'Not Found'
            });

            const response = await apiConfig.fetchWithRetry('https://test.com');
            
            expect(mockFetch).toHaveBeenCalledTimes(1);
            expect(response.status).toBe(404);
        });
    });

    describe('API方法', () => {
        beforeEach(() => {
            mockFetch.mockResolvedValue({
                ok: true,
                json: async () => ({ success: true, data: { address: '<EMAIL>' } })
            });
        });

        test('generateAddress应该调用正确的API', async () => {
            const result = await apiConfig.generateAddress();
            
            expect(mockFetch).toHaveBeenCalledWith(
                'https://test-api.tempmail.com/api/generate-address',
                expect.objectContaining({ method: 'POST' })
            );
            expect(result.success).toBe(true);
        });

        test('getEmails应该调用正确的API', async () => {
            const result = await apiConfig.getEmails('<EMAIL>', '2023-01-01T00:00:00Z');
            
            expect(mockFetch).toHaveBeenCalledWith(
                expect.stringContaining('/api/emails'),
                expect.objectContaining({})
            );
            expect(result.success).toBe(true);
        });

        test('getEmailContent应该调用正确的API', async () => {
            const result = await apiConfig.getEmailContent(123, '<EMAIL>');
            
            expect(mockFetch).toHaveBeenCalledWith(
                expect.stringContaining('/api/email/123'),
                expect.objectContaining({})
            );
            expect(result.success).toBe(true);
        });

        test('deleteEmail应该调用正确的API', async () => {
            const result = await apiConfig.deleteEmail('<EMAIL>');
            
            expect(mockFetch).toHaveBeenCalledWith(
                'https://test-api.tempmail.com/api/delete-email',
                expect.objectContaining({ 
                    method: 'DELETE',
                    body: JSON.stringify({ address: '<EMAIL>' })
                })
            );
            expect(result.success).toBe(true);
        });
    });

    describe('配置更新', () => {
        test('应该正确更新配置', () => {
            apiConfig.updateConfig({
                baseUrl: 'https://new-api.com',
                timeout: 15000
            });

            const config = apiConfig.getConfig();
            expect(config.baseUrl).toBe('https://new-api.com');
            expect(config.timeout).toBe(15000);
            expect(config.retryAttempts).toBe(2); // 保持原有值
        });
    });
});
