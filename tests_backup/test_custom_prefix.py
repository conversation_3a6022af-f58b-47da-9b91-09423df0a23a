#!/usr/bin/env python3
"""
测试自定义前缀功能的脚本
"""

import requests
import json
import sys

API_BASE = "http://127.0.0.1:5000"

def test_custom_prefix():
    """测试自定义前缀功能"""
    print("🧪 测试自定义前缀功能...")
    
    # 测试用例
    test_cases = [
        {
            "name": "有效的自定义前缀",
            "prefix": "mytest",
            "should_succeed": True
        },
        {
            "name": "带连字符的前缀",
            "prefix": "my-test-123",
            "should_succeed": True
        },
        {
            "name": "数字前缀",
            "prefix": "123456",
            "should_succeed": True
        },
        {
            "name": "包含特殊字符的前缀",
            "prefix": "test@email",
            "should_succeed": False
        },
        {
            "name": "过长的前缀",
            "prefix": "a" * 25,  # 25个字符，超过20字符限制
            "should_succeed": False
        },
        {
            "name": "空前缀",
            "prefix": "",
            "should_succeed": False
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试: {test_case['name']}")
        print(f"   前缀: '{test_case['prefix']}'")
        
        try:
            # 发送POST请求
            data = {"custom_prefix": test_case["prefix"]} if test_case["prefix"] else {}
            response = requests.post(
                f"{API_BASE}/api/generate-address",
                json=data,
                timeout=10
            )
            
            result = response.json()
            
            if test_case["should_succeed"]:
                if response.status_code == 201 and result.get("success"):
                    email_address = result["data"]["address"]
                    print(f"   ✅ 成功: {email_address}")
                    
                    # 验证前缀是否正确
                    actual_prefix = email_address.split("@")[0]
                    if test_case["prefix"] and not actual_prefix.startswith(test_case["prefix"]):
                        print(f"   ⚠️  警告: 前缀不匹配，期望以'{test_case['prefix']}'开头，实际是'{actual_prefix}'")
                else:
                    print(f"   ❌ 失败: 应该成功但失败了")
                    print(f"      状态码: {response.status_code}")
                    print(f"      响应: {result}")
            else:
                if response.status_code != 201 or not result.get("success"):
                    print(f"   ✅ 成功: 正确拒绝了无效前缀")
                    print(f"      错误信息: {result.get('error')}")
                else:
                    print(f"   ❌ 失败: 应该拒绝但接受了")
                    print(f"      生成的邮箱: {result['data']['address']}")
                    
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 网络错误: {e}")
        except Exception as e:
            print(f"   ❌ 其他错误: {e}")

def test_no_prefix():
    """测试不提供前缀的情况（应该正常工作）"""
    print("\n🧪 测试默认行为（无自定义前缀）...")
    
    try:
        response = requests.post(f"{API_BASE}/api/generate-address", timeout=10)
        result = response.json()
        
        if response.status_code == 201 and result.get("success"):
            email_address = result["data"]["address"]
            print(f"   ✅ 成功: {email_address}")
        else:
            print(f"   ❌ 失败: {result}")
    except Exception as e:
        print(f"   ❌ 错误: {e}")

if __name__ == "__main__":
    print("🚀 开始测试自定义邮箱前缀功能")
    
    try:
        # 先测试服务器是否可访问
        response = requests.get(f"{API_BASE}/", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器不可访问，请确保应用程序正在运行")
            sys.exit(1)
    except:
        print("❌ 无法连接到服务器，请确保应用程序正在运行在 http://127.0.0.1:5000")
        sys.exit(1)
    
    test_no_prefix()
    test_custom_prefix()
    
    print("\n✨ 测试完成！")
